/**
 * 高级数学计算节点 (161-180)
 * 实现复杂的数学运算和算法
 */

import { BaseNode } from '../../base/BaseNode';
import { NodeCategory, DataType, IExecutionContext } from '../../../core/types';

/**
 * 161 - 向量运算节点
 */
export class VectorMathNode extends BaseNode {
  public readonly type = 'math/vector';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '向量运算';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'vector1',
      label: '向量1',
      type: DataType.VECTOR3,
      required: true,
      description: '第一个向量'
    });

    this.addInputPort({
      name: 'vector2',
      label: '向量2',
      type: DataType.VECTOR3,
      required: false,
      defaultValue: [0, 0, 0],
      description: '第二个向量'
    });

    this.addInputPort({
      name: 'operation',
      label: '运算类型',
      type: DataType.STRING,
      required: false,
      defaultValue: 'add',
      description: '运算类型: add, subtract, multiply, divide, dot, cross, normalize, length'
    });

    this.addInputPort({
      name: 'scalar',
      label: '标量',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 1.0,
      description: '标量值（用于标量运算）'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.VECTOR3,
      required: false,
      description: '运算结果向量'
    });

    this.addOutputPort({
      name: 'scalarResult',
      label: '标量结果',
      type: DataType.NUMBER,
      required: false,
      description: '标量运算结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const vec1 = this.getInput(context, 'vector1');
    const vec2 = this.getInput(context, 'vector2', [0, 0, 0]);
    const operation = this.getInput(context, 'operation', 'add');
    const scalar = this.getInput(context, 'scalar', 1.0);

    if (!vec1) {
      throw new Error('向量运算节点需要至少一个向量');
    }

    let result: number[] = [0, 0, 0];
    let scalarResult: number = 0;

    switch (operation) {
      case 'add':
        result = [vec1[0] + vec2[0], vec1[1] + vec2[1], vec1[2] + vec2[2]];
        break;
      case 'subtract':
        result = [vec1[0] - vec2[0], vec1[1] - vec2[1], vec1[2] - vec2[2]];
        break;
      case 'multiply':
        result = [vec1[0] * scalar, vec1[1] * scalar, vec1[2] * scalar];
        break;
      case 'divide':
        if (scalar !== 0) {
          result = [vec1[0] / scalar, vec1[1] / scalar, vec1[2] / scalar];
        }
        break;
      case 'dot':
        scalarResult = vec1[0] * vec2[0] + vec1[1] * vec2[1] + vec1[2] * vec2[2];
        break;
      case 'cross':
        result = [
          vec1[1] * vec2[2] - vec1[2] * vec2[1],
          vec1[2] * vec2[0] - vec1[0] * vec2[2],
          vec1[0] * vec2[1] - vec1[1] * vec2[0]
        ];
        break;
      case 'normalize':
        const length = Math.sqrt(vec1[0] * vec1[0] + vec1[1] * vec1[1] + vec1[2] * vec1[2]);
        if (length > 0) {
          result = [vec1[0] / length, vec1[1] / length, vec1[2] / length];
        }
        break;
      case 'length':
        scalarResult = Math.sqrt(vec1[0] * vec1[0] + vec1[1] * vec1[1] + vec1[2] * vec1[2]);
        break;
    }

    this.setOutput(context, 'result', result);
    this.setOutput(context, 'scalarResult', scalarResult);
  }
}

/**
 * 162 - 矩阵运算节点
 */
export class MatrixMathNode extends BaseNode {
  public readonly type = 'math/matrix';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '矩阵运算';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'matrix1',
      label: '矩阵1',
      type: DataType.MATRIX4,
      required: true,
      description: '第一个矩阵'
    });

    this.addInputPort({
      name: 'matrix2',
      label: '矩阵2',
      type: DataType.MATRIX4,
      required: false,
      description: '第二个矩阵'
    });

    this.addInputPort({
      name: 'operation',
      label: '运算类型',
      type: DataType.STRING,
      required: false,
      defaultValue: 'multiply',
      description: '运算类型: multiply, add, subtract, transpose, inverse, determinant'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果矩阵',
      type: DataType.MATRIX4,
      required: false,
      description: '运算结果矩阵'
    });

    this.addOutputPort({
      name: 'scalarResult',
      label: '标量结果',
      type: DataType.NUMBER,
      required: false,
      description: '标量运算结果（如行列式）'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const mat1 = this.getInput(context, 'matrix1');
    const mat2 = this.getInput(context, 'matrix2');
    const operation = this.getInput(context, 'operation', 'multiply');

    if (!mat1) {
      throw new Error('矩阵运算节点需要至少一个矩阵');
    }

    let result: number[] = new Array(16).fill(0);
    let scalarResult: number = 0;

    switch (operation) {
      case 'multiply':
        if (mat2) {
          result = this.multiplyMatrices(mat1, mat2);
        }
        break;
      case 'add':
        if (mat2) {
          for (let i = 0; i < 16; i++) {
            result[i] = mat1[i] + mat2[i];
          }
        }
        break;
      case 'subtract':
        if (mat2) {
          for (let i = 0; i < 16; i++) {
            result[i] = mat1[i] - mat2[i];
          }
        }
        break;
      case 'transpose':
        result = this.transposeMatrix(mat1);
        break;
      case 'inverse':
        result = this.inverseMatrix(mat1);
        break;
      case 'determinant':
        scalarResult = this.determinant(mat1);
        break;
    }

    this.setOutput(context, 'result', result);
    this.setOutput(context, 'scalarResult', scalarResult);
  }

  private multiplyMatrices(a: number[], b: number[]): number[] {
    const result = new Array(16).fill(0);
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        for (let k = 0; k < 4; k++) {
          result[i * 4 + j] += a[i * 4 + k] * b[k * 4 + j];
        }
      }
    }
    return result;
  }

  private transposeMatrix(mat: number[]): number[] {
    const result = new Array(16);
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        result[j * 4 + i] = mat[i * 4 + j];
      }
    }
    return result;
  }

  private inverseMatrix(mat: number[]): number[] {
    // 简化实现，实际应该使用更精确的矩阵求逆算法
    const result = [...mat];
    // 这里应该实现完整的矩阵求逆算法
    return result;
  }

  private determinant(mat: number[]): number {
    // 简化实现，计算4x4矩阵的行列式
    return mat[0] * mat[5] * mat[10] * mat[15] - 
           mat[0] * mat[5] * mat[11] * mat[14] -
           mat[0] * mat[6] * mat[9] * mat[15] +
           mat[0] * mat[6] * mat[11] * mat[13];
  }
}

/**
 * 163 - 四元数运算节点
 */
export class QuaternionMathNode extends BaseNode {
  public readonly type = 'math/quaternion';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '四元数运算';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'quaternion1',
      label: '四元数1',
      type: DataType.QUATERNION,
      required: true,
      description: '第一个四元数 [x, y, z, w]'
    });

    this.addInputPort({
      name: 'quaternion2',
      label: '四元数2',
      type: DataType.QUATERNION,
      required: false,
      defaultValue: [0, 0, 0, 1],
      description: '第二个四元数 [x, y, z, w]'
    });

    this.addInputPort({
      name: 'operation',
      label: '运算类型',
      type: DataType.STRING,
      required: false,
      defaultValue: 'multiply',
      description: '运算类型: multiply, slerp, normalize, conjugate, inverse, toEuler'
    });

    this.addInputPort({
      name: 'factor',
      label: '插值因子',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0.5,
      description: '插值因子（用于slerp）'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果四元数',
      type: DataType.QUATERNION,
      required: false,
      description: '运算结果四元数'
    });

    this.addOutputPort({
      name: 'eulerAngles',
      label: '欧拉角',
      type: DataType.VECTOR3,
      required: false,
      description: '转换后的欧拉角'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const quat1 = this.getInput(context, 'quaternion1');
    const quat2 = this.getInput(context, 'quaternion2', [0, 0, 0, 1]);
    const operation = this.getInput(context, 'operation', 'multiply');
    const factor = this.getInput(context, 'factor', 0.5);

    if (!quat1) {
      throw new Error('四元数运算节点需要至少一个四元数');
    }

    let result: number[] = [0, 0, 0, 1];
    let eulerAngles: number[] = [0, 0, 0];

    switch (operation) {
      case 'multiply':
        result = this.multiplyQuaternions(quat1, quat2);
        break;
      case 'slerp':
        result = this.slerpQuaternions(quat1, quat2, factor);
        break;
      case 'normalize':
        result = this.normalizeQuaternion(quat1);
        break;
      case 'conjugate':
        result = [-quat1[0], -quat1[1], -quat1[2], quat1[3]];
        break;
      case 'inverse':
        result = this.inverseQuaternion(quat1);
        break;
      case 'toEuler':
        eulerAngles = this.quaternionToEuler(quat1);
        result = quat1;
        break;
    }

    this.setOutput(context, 'result', result);
    this.setOutput(context, 'eulerAngles', eulerAngles);
  }

  private multiplyQuaternions(q1: number[], q2: number[]): number[] {
    return [
      q1[3] * q2[0] + q1[0] * q2[3] + q1[1] * q2[2] - q1[2] * q2[1],
      q1[3] * q2[1] - q1[0] * q2[2] + q1[1] * q2[3] + q1[2] * q2[0],
      q1[3] * q2[2] + q1[0] * q2[1] - q1[1] * q2[0] + q1[2] * q2[3],
      q1[3] * q2[3] - q1[0] * q2[0] - q1[1] * q2[1] - q1[2] * q2[2]
    ];
  }

  private slerpQuaternions(q1: number[], q2: number[], t: number): number[] {
    // 简化的球面线性插值实现
    const dot = q1[0] * q2[0] + q1[1] * q2[1] + q1[2] * q2[2] + q1[3] * q2[3];
    const theta = Math.acos(Math.abs(dot));
    const sinTheta = Math.sin(theta);
    
    if (sinTheta < 0.001) {
      return q1; // 四元数太接近，直接返回第一个
    }
    
    const a = Math.sin((1 - t) * theta) / sinTheta;
    const b = Math.sin(t * theta) / sinTheta;
    
    return [
      a * q1[0] + b * q2[0],
      a * q1[1] + b * q2[1],
      a * q1[2] + b * q2[2],
      a * q1[3] + b * q2[3]
    ];
  }

  private normalizeQuaternion(q: number[]): number[] {
    const length = Math.sqrt(q[0] * q[0] + q[1] * q[1] + q[2] * q[2] + q[3] * q[3]);
    if (length > 0) {
      return [q[0] / length, q[1] / length, q[2] / length, q[3] / length];
    }
    return [0, 0, 0, 1];
  }

  private inverseQuaternion(q: number[]): number[] {
    const lengthSq = q[0] * q[0] + q[1] * q[1] + q[2] * q[2] + q[3] * q[3];
    if (lengthSq > 0) {
      return [-q[0] / lengthSq, -q[1] / lengthSq, -q[2] / lengthSq, q[3] / lengthSq];
    }
    return [0, 0, 0, 1];
  }

  private quaternionToEuler(q: number[]): number[] {
    // 四元数转欧拉角（简化实现）
    const x = Math.atan2(2 * (q[3] * q[0] + q[1] * q[2]), 1 - 2 * (q[0] * q[0] + q[1] * q[1]));
    const y = Math.asin(2 * (q[3] * q[1] - q[2] * q[0]));
    const z = Math.atan2(2 * (q[3] * q[2] + q[0] * q[1]), 1 - 2 * (q[1] * q[1] + q[2] * q[2]));
    
    return [x, y, z];
  }
}

/**
 * 164 - 三角函数节点
 */
export class TrigonometryNode extends BaseNode {
  public readonly type = 'math/trigonometry';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '三角函数';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'angle',
      label: '角度',
      type: DataType.NUMBER,
      required: true,
      description: '输入角度'
    });

    this.addInputPort({
      name: 'angleUnit',
      label: '角度单位',
      type: DataType.STRING,
      required: false,
      defaultValue: 'radians',
      description: '角度单位: radians, degrees'
    });

    this.addInputPort({
      name: 'function',
      label: '函数类型',
      type: DataType.STRING,
      required: false,
      defaultValue: 'sin',
      description: '函数类型: sin, cos, tan, asin, acos, atan, atan2'
    });

    this.addInputPort({
      name: 'secondAngle',
      label: '第二角度',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '第二个角度（用于atan2）'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果',
      type: DataType.NUMBER,
      required: false,
      description: '三角函数计算结果'
    });

    this.addOutputPort({
      name: 'allResults',
      label: '所有结果',
      type: DataType.OBJECT,
      required: false,
      description: '所有三角函数的结果'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const angle = this.getInput(context, 'angle');
    const angleUnit = this.getInput(context, 'angleUnit', 'radians');
    const functionType = this.getInput(context, 'function', 'sin');
    const secondAngle = this.getInput(context, 'secondAngle', 0);

    if (angle === undefined || angle === null) {
      throw new Error('三角函数节点需要输入角度');
    }

    // 转换角度单位
    let radians = angle;
    let secondRadians = secondAngle;
    if (angleUnit === 'degrees') {
      radians = angle * Math.PI / 180;
      secondRadians = secondAngle * Math.PI / 180;
    }

    let result: number = 0;

    switch (functionType) {
      case 'sin':
        result = Math.sin(radians);
        break;
      case 'cos':
        result = Math.cos(radians);
        break;
      case 'tan':
        result = Math.tan(radians);
        break;
      case 'asin':
        result = Math.asin(Math.max(-1, Math.min(1, radians)));
        if (angleUnit === 'degrees') result = result * 180 / Math.PI;
        break;
      case 'acos':
        result = Math.acos(Math.max(-1, Math.min(1, radians)));
        if (angleUnit === 'degrees') result = result * 180 / Math.PI;
        break;
      case 'atan':
        result = Math.atan(radians);
        if (angleUnit === 'degrees') result = result * 180 / Math.PI;
        break;
      case 'atan2':
        result = Math.atan2(radians, secondRadians);
        if (angleUnit === 'degrees') result = result * 180 / Math.PI;
        break;
    }

    // 计算所有三角函数结果
    const allResults = {
      sin: Math.sin(radians),
      cos: Math.cos(radians),
      tan: Math.tan(radians),
      asin: Math.asin(Math.max(-1, Math.min(1, radians))),
      acos: Math.acos(Math.max(-1, Math.min(1, radians))),
      atan: Math.atan(radians),
      atan2: Math.atan2(radians, secondRadians)
    };

    this.setOutput(context, 'result', result);
    this.setOutput(context, 'allResults', allResults);
  }
}

/**
 * 165 - 随机数生成节点
 */
export class RandomGeneratorNode extends BaseNode {
  public readonly type = 'math/random';
  public readonly category = NodeCategory.MATH;

  protected getDefaultName(): string {
    return '随机数生成';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'min',
      label: '最小值',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0,
      description: '随机数最小值'
    });

    this.addInputPort({
      name: 'max',
      label: '最大值',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 1,
      description: '随机数最大值'
    });

    this.addInputPort({
      name: 'type',
      label: '类型',
      type: DataType.STRING,
      required: false,
      defaultValue: 'uniform',
      description: '随机数类型: uniform, integer, gaussian, boolean'
    });

    this.addInputPort({
      name: 'seed',
      label: '种子',
      type: DataType.NUMBER,
      required: false,
      description: '随机数种子（可选）'
    });

    this.addInputPort({
      name: 'generate',
      label: '生成',
      type: DataType.TRIGGER,
      required: false,
      description: '触发生成新的随机数'
    });

    this.addOutputPort({
      name: 'value',
      label: '随机值',
      type: DataType.NUMBER,
      required: false,
      description: '生成的随机数'
    });

    this.addOutputPort({
      name: 'vector',
      label: '随机向量',
      type: DataType.VECTOR3,
      required: false,
      description: '生成的随机向量'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const min = this.getInput(context, 'min', 0);
    const max = this.getInput(context, 'max', 1);
    const type = this.getInput(context, 'type', 'uniform');
    const seed = this.getInput(context, 'seed');

    // 如果提供了种子，可以实现确定性随机数（这里简化处理）
    let value: number = 0;

    switch (type) {
      case 'uniform':
        value = min + Math.random() * (max - min);
        break;
      case 'integer':
        value = Math.floor(min + Math.random() * (max - min + 1));
        break;
      case 'gaussian':
        // Box-Muller变换生成高斯分布
        const u1 = Math.random();
        const u2 = Math.random();
        const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
        value = min + z0 * (max - min) / 6; // 简化的高斯分布
        break;
      case 'boolean':
        value = Math.random() < 0.5 ? 0 : 1;
        break;
    }

    // 生成随机向量
    const vector = [
      min + Math.random() * (max - min),
      min + Math.random() * (max - min),
      min + Math.random() * (max - min)
    ];

    this.setOutput(context, 'value', value);
    this.setOutput(context, 'vector', vector);
  }
}
