/**
 * 第四批次节点编辑器集成 (151-200)
 * 数学计算和数据流节点在编辑器中的集成
 */

import { NodeEditor } from '../NodeEditor';
import { VisualScriptNode } from '../../../libs/dl-engine-types';
import { registerBatch4Nodes } from '../../../libs/dl-engine';

/**
 * 第四批次节点配置接口
 */
interface Batch4NodeConfig {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  color: string;
  tags: string[];
  nodeClass: any;
}

/**
 * 第四批次节点编辑器集成类
 */
export class Batch4NodesIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Map<string, Batch4NodeConfig> = new Map();
  private categoryNodes: Map<string, Batch4NodeConfig[]> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
    this.initializeNodes();
  }

  /**
   * 初始化第四批次节点
   */
  private initializeNodes(): void {
    this.registerAdvancedTransformNodes();
    this.registerAdvancedMathNodes();
    this.registerDataFlowNodes();
    this.setupNodeCategories();
    this.setupNodePalette();
  }

  /**
   * 注册高级变换节点 (151-160)
   */
  private registerAdvancedTransformNodes(): void {
    const transformNodes = [
      'InterpolateTransform',
      'TransformConstraint', 
      'DistanceCalculate',
      'DirectionCalculate',
      'AngleCalculate',
      'TransformAnimation',
      'TransformEasing',
      'TransformPath',
      'TransformSync'
    ];

    transformNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Transform/Advanced',
        icon: this.getNodeIcon(nodeType),
        color: '#FF9800',
        tags: ['transform', 'advanced', 'batch4'],
        nodeClass: null
      });
    });
  }

  /**
   * 注册高级数学节点 (161-180)
   */
  private registerAdvancedMathNodes(): void {
    const mathNodes = [
      'VectorMath',
      'MatrixMath',
      'QuaternionMath',
      'Trigonometry',
      'RandomGenerator',
      'NoiseGenerator',
      'Interpolation',
      'CurveCalculation',
      'Statistics',
      'GeometryMath',
      'PhysicsConstants',
      'UnitConversion',
      'NumberRange',
      'NumberMapping',
      'NumberCompare',
      'LogicOperations',
      'BitwiseOperations',
      'MathConstants',
      'ComplexMath',
      'AlgorithmImplementation'
    ];

    mathNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Math/Advanced',
        icon: this.getNodeIcon(nodeType),
        color: '#2196F3',
        tags: ['math', 'advanced', 'calculation', 'batch4'],
        nodeClass: null
      });
    });
  }

  /**
   * 注册数据流节点 (181-200)
   */
  private registerDataFlowNodes(): void {
    const dataFlowNodes = [
      'DataInput',
      'DataOutput',
      'DataConvert',
      'DataValidate',
      'DataFilter',
      'DataSort',
      'DataAggregate',
      'DataCache',
      'FlowControl',
      'ConditionalBranch',
      'LoopControl',
      'ExceptionHandle',
      'DataBinding',
      'DataWatch',
      'DataSerialize',
      'DataDeserialize',
      'DataCompress',
      'DataEncrypt',
      'DataDecrypt',
      'DataSync'
    ];

    dataFlowNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Data/Flow',
        icon: this.getNodeIcon(nodeType),
        color: '#4CAF50',
        tags: ['data', 'flow', 'processing', 'batch4'],
        nodeClass: null
      });
    });
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    // 高级变换分类
    this.nodeEditor.addCategory({
      id: 'transform-advanced',
      name: '高级变换',
      description: '复杂的变换操作和动画效果',
      icon: 'transform',
      color: '#FF9800',
      order: 4
    });

    // 高级数学分类
    this.nodeEditor.addCategory({
      id: 'math-advanced',
      name: '高级数学',
      description: '复杂的数学运算和算法',
      icon: 'functions',
      color: '#2196F3',
      order: 5
    });

    // 数据流分类
    this.nodeEditor.addCategory({
      id: 'data-flow',
      name: '数据流',
      description: '数据处理和流程控制',
      icon: 'data_usage',
      color: '#4CAF50',
      order: 6
    });
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 添加到节点面板
    this.categoryNodes.forEach((nodes, category) => {
      this.nodeEditor.addNodeGroup({
        category,
        nodes: nodes.map(node => ({
          type: node.type,
          name: node.name,
          description: node.description,
          icon: node.icon,
          color: node.color,
          tags: node.tags
        }))
      });
    });

    // 设置拖拽处理
    this.setupDragAndDrop();
  }

  /**
   * 设置拖拽功能
   */
  private setupDragAndDrop(): void {
    this.nodeEditor.onNodeDragStart((nodeType: string) => {
      const nodeConfig = this.registeredNodes.get(nodeType);
      if (nodeConfig) {
        return {
          type: nodeConfig.type,
          name: nodeConfig.name,
          category: nodeConfig.category,
          icon: nodeConfig.icon,
          color: nodeConfig.color
        };
      }
      return null;
    });

    this.nodeEditor.onNodeDrop((nodeType: string, position: { x: number; y: number }) => {
      return this.createNodeInstance(nodeType, position);
    });
  }

  /**
   * 创建节点实例
   */
  private createNodeInstance(nodeType: string, position: { x: number; y: number }): VisualScriptNode | null {
    const nodeConfig = this.registeredNodes.get(nodeType);
    if (!nodeConfig) {
      console.error(`未找到节点类型: ${nodeType}`);
      return null;
    }

    try {
      // 这里应该通过节点注册表创建实际的节点实例
      const nodeInstance = {
        id: this.generateNodeId(),
        type: nodeConfig.type,
        name: nodeConfig.name,
        position,
        properties: {},
        inputs: {},
        outputs: {}
      };

      return nodeInstance as VisualScriptNode;
    } catch (error) {
      console.error(`创建节点失败 ${nodeType}:`, error);
      return null;
    }
  }

  /**
   * 注册节点
   */
  private registerNode(config: Batch4NodeConfig): void {
    this.registeredNodes.set(config.type, config);
    
    // 添加到分类
    const categoryNodes = this.categoryNodes.get(config.category) || [];
    categoryNodes.push(config);
    this.categoryNodes.set(config.category, categoryNodes);
  }

  /**
   * 获取节点显示名称
   */
  private getNodeDisplayName(nodeType: string): string {
    const nameMap: { [key: string]: string } = {
      'InterpolateTransform': '插值变换',
      'TransformConstraint': '变换约束',
      'DistanceCalculate': '距离计算',
      'DirectionCalculate': '方向计算',
      'AngleCalculate': '角度计算',
      'TransformAnimation': '变换动画',
      'VectorMath': '向量运算',
      'MatrixMath': '矩阵运算',
      'QuaternionMath': '四元数运算',
      'Trigonometry': '三角函数',
      'RandomGenerator': '随机数生成',
      'DataInput': '数据输入',
      'DataOutput': '数据输出',
      'DataConvert': '数据转换',
      'DataValidate': '数据验证',
      'DataFilter': '数据过滤'
    };
    return nameMap[nodeType] || nodeType;
  }

  /**
   * 获取节点描述
   */
  private getNodeDescription(nodeType: string): string {
    const descMap: { [key: string]: string } = {
      'InterpolateTransform': '在两个变换之间进行插值计算',
      'TransformConstraint': '对变换应用约束限制',
      'DistanceCalculate': '计算两个位置之间的距离',
      'DirectionCalculate': '计算从一个位置到另一个位置的方向',
      'AngleCalculate': '计算两个向量之间的夹角',
      'TransformAnimation': '创建变换动画效果',
      'VectorMath': '执行向量数学运算',
      'MatrixMath': '执行矩阵数学运算',
      'QuaternionMath': '执行四元数数学运算',
      'Trigonometry': '执行三角函数计算',
      'RandomGenerator': '生成各种类型的随机数',
      'DataInput': '从外部源读取数据',
      'DataOutput': '将数据输出到外部目标',
      'DataConvert': '转换数据类型',
      'DataValidate': '验证数据的有效性',
      'DataFilter': '过滤数据数组'
    };
    return descMap[nodeType] || `${nodeType} 节点`;
  }

  /**
   * 获取节点图标
   */
  private getNodeIcon(nodeType: string): string {
    const iconMap: { [key: string]: string } = {
      'InterpolateTransform': 'timeline',
      'TransformConstraint': 'lock',
      'DistanceCalculate': 'straighten',
      'DirectionCalculate': 'navigation',
      'AngleCalculate': 'rotate_right',
      'TransformAnimation': 'movie',
      'VectorMath': 'vector_icon',
      'MatrixMath': 'grid_on',
      'QuaternionMath': 'rotate_3d',
      'Trigonometry': 'functions',
      'RandomGenerator': 'shuffle',
      'DataInput': 'input',
      'DataOutput': 'output',
      'DataConvert': 'transform',
      'DataValidate': 'verified',
      'DataFilter': 'filter_list'
    };
    return iconMap[nodeType] || 'extension';
  }

  /**
   * 生成节点ID
   */
  private generateNodeId(): string {
    return 'node_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取已注册的节点
   */
  public getRegisteredNodes(): Map<string, Batch4NodeConfig> {
    return this.registeredNodes;
  }

  /**
   * 获取分类节点
   */
  public getCategoryNodes(): Map<string, Batch4NodeConfig[]> {
    return this.categoryNodes;
  }

  /**
   * 销毁集成
   */
  public dispose(): void {
    this.registeredNodes.clear();
    this.categoryNodes.clear();
  }
}
