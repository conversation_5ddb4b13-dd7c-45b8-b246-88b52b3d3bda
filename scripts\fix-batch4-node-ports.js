/**
 * 修复第四批次节点端口定义的脚本
 * 为所有端口添加direction属性
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '../engine/src/visual-script-v2/nodes/registry/Batch4NodesRegistry.ts');

function fixNodePorts() {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 修复inputs中的端口定义
    content = content.replace(
      /{ name: '([^']+)', label: '([^']+)', type: DataType\.([^,]+), required: (true|false)([^}]*) }/g,
      (match, name, label, type, required, rest) => {
        // 检查是否已经有direction属性
        if (match.includes('direction:')) {
          return match;
        }
        
        // 根据上下文判断是input还是output
        const beforeMatch = content.substring(0, content.indexOf(match));
        const isInInputs = beforeMatch.lastIndexOf('inputs:') > beforeMatch.lastIndexOf('outputs:');
        const direction = isInInputs ? 'input' : 'output';
        
        return `{ name: '${name}', label: '${label}', type: DataType.${type}, direction: '${direction}', required: ${required}${rest} }`;
      }
    );
    
    // 特殊处理一些复杂的情况
    content = content.replace(
      /{ name: '([^']+)', label: '([^']+)', type: DataType\.([^,]+), required: (true|false), defaultValue: ([^}]+) }/g,
      (match, name, label, type, required, defaultValue) => {
        if (match.includes('direction:')) {
          return match;
        }
        
        const beforeMatch = content.substring(0, content.indexOf(match));
        const isInInputs = beforeMatch.lastIndexOf('inputs:') > beforeMatch.lastIndexOf('outputs:');
        const direction = isInInputs ? 'input' : 'output';
        
        return `{ name: '${name}', label: '${label}', type: DataType.${type}, direction: '${direction}', required: ${required}, defaultValue: ${defaultValue} }`;
      }
    );
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('✅ 第四批次节点端口定义修复完成');
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
  }
}

fixNodePorts();
