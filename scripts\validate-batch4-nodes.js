/**
 * 验证第四批次节点实现的脚本
 */

const fs = require('fs');
const path = require('path');

function validateBatch4Nodes() {
  console.log('🔍 验证第四批次节点实现...\n');

  const results = {
    advancedTransform: validateAdvancedTransformNodes(),
    advancedMath: validateAdvancedMathNodes(),
    dataFlow: validateDataFlowNodes(),
    registry: validateRegistry(),
    editorIntegration: validateEditorIntegration(),
    tests: validateTests()
  };

  // 输出验证结果
  console.log('\n📊 验证结果汇总:');
  console.log('================');
  
  let totalPassed = 0;
  let totalTests = 0;
  
  Object.entries(results).forEach(([category, result]) => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${category}: ${result.passed}/${result.total} 通过`);
    if (result.details) {
      result.details.forEach(detail => console.log(`   - ${detail}`));
    }
    totalPassed += result.passed;
    totalTests += result.total;
  });
  
  console.log(`\n🎯 总体结果: ${totalPassed}/${totalTests} 通过`);
  
  if (totalPassed === totalTests) {
    console.log('🎉 第四批次节点实现验证通过！');
  } else {
    console.log('⚠️  存在需要修复的问题');
  }
}

function validateAdvancedTransformNodes() {
  const filePath = 'engine/src/visual-script-v2/nodes/categories/transform/AdvancedTransformNodes.ts';
  
  if (!fs.existsSync(filePath)) {
    return { passed: 0, total: 1, details: ['文件不存在'] };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const checks = [
    { name: 'InterpolateTransformNode', pattern: /export class InterpolateTransformNode/ },
    { name: 'TransformConstraintNode', pattern: /export class TransformConstraintNode/ },
    { name: 'DistanceCalculateNode', pattern: /export class DistanceCalculateNode/ },
    { name: 'DirectionCalculateNode', pattern: /export class DirectionCalculateNode/ },
    { name: 'AngleCalculateNode', pattern: /export class AngleCalculateNode/ },
    { name: 'TransformAnimationNode', pattern: /export class TransformAnimationNode/ }
  ];
  
  const passed = checks.filter(check => check.pattern.test(content)).length;
  const details = checks
    .filter(check => !check.pattern.test(content))
    .map(check => `缺少 ${check.name}`);
  
  return { passed, total: checks.length, details };
}

function validateAdvancedMathNodes() {
  const filePath = 'engine/src/visual-script-v2/nodes/categories/math/AdvancedMathNodes.ts';
  
  if (!fs.existsSync(filePath)) {
    return { passed: 0, total: 1, details: ['文件不存在'] };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const checks = [
    { name: 'VectorMathNode', pattern: /export class VectorMathNode/ },
    { name: 'MatrixMathNode', pattern: /export class MatrixMathNode/ },
    { name: 'QuaternionMathNode', pattern: /export class QuaternionMathNode/ },
    { name: 'TrigonometryNode', pattern: /export class TrigonometryNode/ },
    { name: 'RandomGeneratorNode', pattern: /export class RandomGeneratorNode/ }
  ];
  
  const passed = checks.filter(check => check.pattern.test(content)).length;
  const details = checks
    .filter(check => !check.pattern.test(content))
    .map(check => `缺少 ${check.name}`);
  
  return { passed, total: checks.length, details };
}

function validateDataFlowNodes() {
  const filePath = 'engine/src/visual-script-v2/nodes/categories/data/DataFlowNodes.ts';
  
  if (!fs.existsSync(filePath)) {
    return { passed: 0, total: 1, details: ['文件不存在'] };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const checks = [
    { name: 'DataInputNode', pattern: /export class DataInputNode/ },
    { name: 'DataOutputNode', pattern: /export class DataOutputNode/ },
    { name: 'DataConvertNode', pattern: /export class DataConvertNode/ },
    { name: 'DataValidateNode', pattern: /export class DataValidateNode/ },
    { name: 'DataFilterNode', pattern: /export class DataFilterNode/ }
  ];
  
  const passed = checks.filter(check => check.pattern.test(content)).length;
  const details = checks
    .filter(check => !check.pattern.test(content))
    .map(check => `缺少 ${check.name}`);
  
  return { passed, total: checks.length, details };
}

function validateRegistry() {
  const filePath = 'engine/src/visual-script-v2/nodes/registry/Batch4NodesRegistry.ts';
  
  if (!fs.existsSync(filePath)) {
    return { passed: 0, total: 1, details: ['注册表文件不存在'] };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const checks = [
    { name: '注册函数', pattern: /export function registerBatch4Nodes/ },
    { name: '高级变换节点定义', pattern: /advancedTransformNodeDefinitions/ },
    { name: '高级数学节点定义', pattern: /advancedMathNodeDefinitions/ },
    { name: '数据流节点定义', pattern: /dataFlowNodeDefinitions/ }
  ];
  
  const passed = checks.filter(check => check.pattern.test(content)).length;
  const details = checks
    .filter(check => !check.pattern.test(content))
    .map(check => `缺少 ${check.name}`);
  
  return { passed, total: checks.length, details };
}

function validateEditorIntegration() {
  const filePath = 'editor/src/components/visual-script/nodes/Batch4NodesIntegration.ts';
  
  if (!fs.existsSync(filePath)) {
    return { passed: 0, total: 1, details: ['编辑器集成文件不存在'] };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const checks = [
    { name: '集成类', pattern: /export class Batch4NodesIntegration/ },
    { name: '高级变换节点注册', pattern: /registerAdvancedTransformNodes/ },
    { name: '高级数学节点注册', pattern: /registerAdvancedMathNodes/ },
    { name: '数据流节点注册', pattern: /registerDataFlowNodes/ }
  ];
  
  const passed = checks.filter(check => check.pattern.test(content)).length;
  const details = checks
    .filter(check => !check.pattern.test(content))
    .map(check => `缺少 ${check.name}`);
  
  return { passed, total: checks.length, details };
}

function validateTests() {
  const filePath = 'tests/visual-script/nodes/Batch4NodesTest.ts';
  
  if (!fs.existsSync(filePath)) {
    return { passed: 0, total: 1, details: ['测试文件不存在'] };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const checks = [
    { name: '高级变换节点测试', pattern: /describe.*高级变换节点/ },
    { name: '高级数学节点测试', pattern: /describe.*高级数学节点/ },
    { name: '数据流节点测试', pattern: /describe.*数据流节点/ },
    { name: '集成测试', pattern: /describe.*集成测试/ }
  ];
  
  const passed = checks.filter(check => check.pattern.test(content)).length;
  const details = checks
    .filter(check => !check.pattern.test(content))
    .map(check => `缺少 ${check.name}`);
  
  return { passed, total: checks.length, details };
}

// 运行验证
validateBatch4Nodes();
