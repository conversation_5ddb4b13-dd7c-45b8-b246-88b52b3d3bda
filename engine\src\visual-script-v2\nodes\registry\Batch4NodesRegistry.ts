/**
 * 第四批次节点注册表 (151-200)
 * 数学计算和数据流节点注册
 */

import { NodeRegistry } from './NodeRegistry';
import { NodeDefinition, NodeCategory, DataType } from '../../core/types';

// 导入高级变换节点 (151-160)
import {
  InterpolateTransformNode,
  TransformConstraintNode,
  DistanceCalculateNode,
  DirectionCalculateNode,
  AngleCalculateNode,
  TransformAnimationNode
} from '../categories/transform/AdvancedTransformNodes';

// 导入高级数学节点 (161-180)
import {
  VectorMathNode,
  MatrixMathNode,
  QuaternionMathNode,
  TrigonometryNode,
  RandomGeneratorNode
} from '../categories/math/AdvancedMathNodes';

// 导入数据流节点 (181-200)
import {
  DataInputNode,
  DataOutputNode,
  DataConvertNode,
  DataValidateNode,
  DataFilterNode
} from '../categories/data/DataFlowNodes';

/**
 * 高级变换节点定义 (151-160)
 */
const advancedTransformNodeDefinitions: NodeDefinition[] = [
  {
    type: 'transform/interpolate',
    name: '插值变换',
    description: '在两个变换之间进行插值计算',
    category: NodeCategory.TRANSFORM,
    icon: 'timeline',
    color: '#FF9800',
    tags: ['transform', 'interpolation', 'animation', 'batch4'],
    inputs: [
      { name: 'fromTransform', label: '起始变换', type: DataType.MATRIX4, direction: 'input', required: true },
      { name: 'toTransform', label: '目标变换', type: DataType.MATRIX4, direction: 'input', required: true },
      { name: 'factor', label: '插值因子', type: DataType.NUMBER, direction: 'input', required: false, defaultValue: 0.5 },
      { name: 'interpolationType', label: '插值类型', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'linear' }
    ],
    outputs: [
      { name: 'result', label: '结果变换', type: DataType.MATRIX4, direction: 'output', required: false }
    ],
    nodeClass: InterpolateTransformNode
  },
  {
    type: 'transform/constraint',
    name: '变换约束',
    description: '对变换应用约束限制',
    category: NodeCategory.TRANSFORM,
    icon: 'lock',
    color: '#FF9800',
    tags: ['transform', 'constraint', 'limit', 'batch4'],
    inputs: [
      { name: 'transform', label: '输入变换', type: DataType.MATRIX4, direction: 'input', required: true },
      { name: 'constraintType', label: '约束类型', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'position' },
      { name: 'minValues', label: '最小值', type: DataType.VECTOR3, direction: 'input', required: false, defaultValue: [-10, -10, -10] },
      { name: 'maxValues', label: '最大值', type: DataType.VECTOR3, direction: 'input', required: false, defaultValue: [10, 10, 10] }
    ],
    outputs: [
      { name: 'constrainedTransform', label: '约束后变换', type: DataType.MATRIX4, direction: 'output', required: false }
    ],
    nodeClass: TransformConstraintNode
  },
  {
    type: 'transform/distance',
    name: '距离计算',
    description: '计算两个位置之间的距离',
    category: NodeCategory.TRANSFORM,
    icon: 'straighten',
    color: '#FF9800',
    tags: ['transform', 'distance', 'calculation', 'batch4'],
    inputs: [
      { name: 'position1', label: '位置1', type: DataType.VECTOR3, direction: 'input', required: true },
      { name: 'position2', label: '位置2', type: DataType.VECTOR3, direction: 'input', required: true },
      { name: 'distanceType', label: '距离类型', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'euclidean' }
    ],
    outputs: [
      { name: 'distance', label: '距离', type: DataType.NUMBER, direction: 'output', required: false },
      { name: 'direction', label: '方向向量', type: DataType.VECTOR3, direction: 'output', required: false }
    ],
    nodeClass: DistanceCalculateNode
  },
  {
    type: 'transform/direction',
    name: '方向计算',
    description: '计算从一个位置到另一个位置的方向',
    category: NodeCategory.TRANSFORM,
    icon: 'navigation',
    color: '#FF9800',
    tags: ['transform', 'direction', 'vector', 'batch4'],
    inputs: [
      { name: 'fromPosition', label: '起始位置', type: DataType.VECTOR3, direction: 'input', required: true },
      { name: 'toPosition', label: '目标位置', type: DataType.VECTOR3, direction: 'input', required: true },
      { name: 'normalize', label: '归一化', type: DataType.BOOLEAN, direction: 'input', required: false, defaultValue: true }
    ],
    outputs: [
      { name: 'direction', label: '方向向量', type: DataType.VECTOR3, direction: 'output', required: false },
      { name: 'magnitude', label: '长度', type: DataType.NUMBER, direction: 'output', required: false }
    ],
    nodeClass: DirectionCalculateNode
  },
  {
    type: 'transform/angle',
    name: '角度计算',
    description: '计算两个向量之间的夹角',
    category: NodeCategory.TRANSFORM,
    icon: 'rotate_right',
    color: '#FF9800',
    tags: ['transform', 'angle', 'vector', 'batch4'],
    inputs: [
      { name: 'vector1', label: '向量1', type: DataType.VECTOR3, direction: 'input', required: true },
      { name: 'vector2', label: '向量2', type: DataType.VECTOR3, direction: 'input', required: true },
      { name: 'angleUnit', label: '角度单位', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'radians' }
    ],
    outputs: [
      { name: 'angle', label: '夹角', type: DataType.NUMBER, direction: 'output', required: false },
      { name: 'dotProduct', label: '点积', type: DataType.NUMBER, direction: 'output', required: false },
      { name: 'crossProduct', label: '叉积', type: DataType.VECTOR3, direction: 'output', required: false }
    ],
    nodeClass: AngleCalculateNode
  },
  {
    type: 'transform/animation',
    name: '变换动画',
    description: '创建变换动画效果',
    category: NodeCategory.TRANSFORM,
    icon: 'movie',
    color: '#FF9800',
    tags: ['transform', 'animation', 'tween', 'batch4'],
    inputs: [
      { name: 'startTransform', label: '起始变换', type: DataType.MATRIX4, direction: 'input', required: true },
      { name: 'endTransform', label: '结束变换', type: DataType.MATRIX4, direction: 'input', required: true },
      { name: 'duration', label: '持续时间', type: DataType.NUMBER, direction: 'input', required: false, defaultValue: 1.0 },
      { name: 'currentTime', label: '当前时间', type: DataType.NUMBER, direction: 'input', required: false, defaultValue: 0.0 },
      { name: 'loop', label: '循环', type: DataType.BOOLEAN, direction: 'input', required: false, defaultValue: false }
    ],
    outputs: [
      { name: 'currentTransform', label: '当前变换', type: DataType.MATRIX4, direction: 'output', required: false },
      { name: 'progress', label: '进度', type: DataType.NUMBER, direction: 'output', required: false },
      { name: 'isComplete', label: '是否完成', type: DataType.BOOLEAN, direction: 'output', required: false }
    ],
    nodeClass: TransformAnimationNode
  }
];

/**
 * 高级数学节点定义 (161-180)
 */
const advancedMathNodeDefinitions: NodeDefinition[] = [
  {
    type: 'math/vector',
    name: '向量运算',
    description: '执行向量数学运算',
    category: NodeCategory.MATH,
    icon: 'vector_icon',
    color: '#2196F3',
    tags: ['math', 'vector', 'calculation', 'batch4'],
    inputs: [
      { name: 'vector1', label: '向量1', type: DataType.VECTOR3, direction: 'input', required: true },
      { name: 'vector2', label: '向量2', type: DataType.VECTOR3, direction: 'input', required: false, defaultValue: [0, 0, 0] },
      { name: 'operation', label: '运算类型', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'add' },
      { name: 'scalar', label: '标量', type: DataType.NUMBER, direction: 'input', required: false, defaultValue: 1.0 }
    ],
    outputs: [
      { name: 'result', label: '结果', type: DataType.VECTOR3, direction: 'output', required: false },
      { name: 'scalarResult', label: '标量结果', type: DataType.NUMBER, direction: 'output', required: false }
    ],
    nodeClass: VectorMathNode
  },
  {
    type: 'math/matrix',
    name: '矩阵运算',
    description: '执行矩阵数学运算',
    category: NodeCategory.MATH,
    icon: 'grid_on',
    color: '#2196F3',
    tags: ['math', 'matrix', 'calculation', 'batch4'],
    inputs: [
      { name: 'matrix1', label: '矩阵1', type: DataType.MATRIX4, direction: 'input', required: true },
      { name: 'matrix2', label: '矩阵2', type: DataType.MATRIX4, direction: 'input', required: false },
      { name: 'operation', label: '运算类型', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'multiply' }
    ],
    outputs: [
      { name: 'result', label: '结果矩阵', type: DataType.MATRIX4, direction: 'output', required: false },
      { name: 'scalarResult', label: '标量结果', type: DataType.NUMBER, direction: 'output', required: false }
    ],
    nodeClass: MatrixMathNode
  },
  {
    type: 'math/quaternion',
    name: '四元数运算',
    description: '执行四元数数学运算',
    category: NodeCategory.MATH,
    icon: 'rotate_3d',
    color: '#2196F3',
    tags: ['math', 'quaternion', 'rotation', 'batch4'],
    inputs: [
      { name: 'quaternion1', label: '四元数1', type: DataType.QUATERNION, direction: 'input', required: true },
      { name: 'quaternion2', label: '四元数2', type: DataType.QUATERNION, direction: 'input', required: false, defaultValue: [0, 0, 0, 1] },
      { name: 'operation', label: '运算类型', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'multiply' },
      { name: 'factor', label: '插值因子', type: DataType.NUMBER, direction: 'input', required: false, defaultValue: 0.5 }
    ],
    outputs: [
      { name: 'result', label: '结果四元数', type: DataType.QUATERNION, direction: 'output', required: false },
      { name: 'eulerAngles', label: '欧拉角', type: DataType.VECTOR3, direction: 'output', required: false }
    ],
    nodeClass: QuaternionMathNode
  },
  {
    type: 'math/trigonometry',
    name: '三角函数',
    description: '执行三角函数计算',
    category: NodeCategory.MATH,
    icon: 'functions',
    color: '#2196F3',
    tags: ['math', 'trigonometry', 'function', 'batch4'],
    inputs: [
      { name: 'angle', label: '角度', type: DataType.NUMBER, direction: 'input', required: true },
      { name: 'angleUnit', label: '角度单位', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'radians' },
      { name: 'function', label: '函数类型', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'sin' },
      { name: 'secondAngle', label: '第二角度', type: DataType.NUMBER, direction: 'input', required: false, defaultValue: 0 }
    ],
    outputs: [
      { name: 'result', label: '结果', type: DataType.NUMBER, direction: 'output', required: false },
      { name: 'allResults', label: '所有结果', type: DataType.OBJECT, direction: 'output', required: false }
    ],
    nodeClass: TrigonometryNode
  },
  {
    type: 'math/random',
    name: '随机数生成',
    description: '生成各种类型的随机数',
    category: NodeCategory.MATH,
    icon: 'shuffle',
    color: '#2196F3',
    tags: ['math', 'random', 'generator', 'batch4'],
    inputs: [
      { name: 'min', label: '最小值', type: DataType.NUMBER, direction: 'input', required: false, defaultValue: 0 },
      { name: 'max', label: '最大值', type: DataType.NUMBER, direction: 'input', required: false, defaultValue: 1 },
      { name: 'type', label: '类型', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'uniform' },
      { name: 'seed', label: '种子', type: DataType.NUMBER, direction: 'input', required: false },
      { name: 'generate', label: '生成', type: DataType.TRIGGER, direction: 'input', required: false }
    ],
    outputs: [
      { name: 'value', label: '随机值', type: DataType.NUMBER, direction: 'output', required: false },
      { name: 'vector', label: '随机向量', type: DataType.VECTOR3, direction: 'output', required: false }
    ],
    nodeClass: RandomGeneratorNode
  }
];

/**
 * 数据流节点定义 (181-200)
 */
const dataFlowNodeDefinitions: NodeDefinition[] = [
  {
    type: 'data/input',
    name: '数据输入',
    description: '从外部源读取数据',
    category: NodeCategory.DATA,
    icon: 'input',
    color: '#4CAF50',
    tags: ['data', 'input', 'io', 'batch4'],
    inputs: [
      { name: 'source', label: '数据源', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'file' },
      { name: 'path', label: '路径', type: DataType.STRING, direction: 'input', required: false },
      { name: 'format', label: '格式', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'json' },
      { name: 'trigger', label: '触发', type: DataType.TRIGGER, direction: 'input', required: false }
    ],
    outputs: [
      { name: 'data', label: '数据', type: DataType.ANY, direction: 'output', required: false },
      { name: 'success', label: '成功', type: DataType.BOOLEAN, direction: 'output', required: false },
      { name: 'error', label: '错误', type: DataType.STRING, direction: 'output', required: false }
    ],
    nodeClass: DataInputNode
  },
  {
    type: 'data/output',
    name: '数据输出',
    description: '将数据输出到外部目标',
    category: NodeCategory.DATA,
    icon: 'output',
    color: '#4CAF50',
    tags: ['data', 'output', 'io', 'batch4'],
    inputs: [
      { name: 'data', label: '数据', type: DataType.ANY, direction: 'input', required: true },
      { name: 'destination', label: '目标', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'file' },
      { name: 'path', label: '路径', type: DataType.STRING, direction: 'input', required: false },
      { name: 'format', label: '格式', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'json' },
      { name: 'trigger', label: '触发', type: DataType.TRIGGER, direction: 'input', required: false }
    ],
    outputs: [
      { name: 'success', label: '成功', type: DataType.BOOLEAN, direction: 'output', required: false },
      { name: 'error', label: '错误', type: DataType.STRING, direction: 'output', required: false }
    ],
    nodeClass: DataOutputNode
  },
  {
    type: 'data/convert',
    name: '数据转换',
    description: '转换数据类型',
    category: NodeCategory.DATA,
    icon: 'transform',
    color: '#4CAF50',
    tags: ['data', 'convert', 'type', 'batch4'],
    inputs: [
      { name: 'input', label: '输入数据', type: DataType.ANY, direction: 'input', required: true },
      { name: 'fromType', label: '源类型', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'auto' },
      { name: 'toType', label: '目标类型', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'string' }
    ],
    outputs: [
      { name: 'output', label: '输出数据', type: DataType.ANY, direction: 'output', required: false },
      { name: 'success', label: '成功', type: DataType.BOOLEAN, direction: 'output', required: false }
    ],
    nodeClass: DataConvertNode
  },
  {
    type: 'data/validate',
    name: '数据验证',
    description: '验证数据的有效性',
    category: NodeCategory.DATA,
    icon: 'verified',
    color: '#4CAF50',
    tags: ['data', 'validate', 'check', 'batch4'],
    inputs: [
      { name: 'data', label: '数据', type: DataType.ANY, direction: 'input', required: true },
      { name: 'rules', label: '验证规则', type: DataType.OBJECT, direction: 'input', required: false },
      { name: 'required', label: '必填', type: DataType.BOOLEAN, direction: 'input', required: false, defaultValue: true },
      { name: 'type', label: '类型', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'any' }
    ],
    outputs: [
      { name: 'isValid', label: '有效', type: DataType.BOOLEAN, direction: 'output', required: false },
      { name: 'errors', label: '错误', type: DataType.ARRAY, direction: 'output', required: false },
      { name: 'validData', label: '有效数据', type: DataType.ANY, direction: 'output', required: false }
    ],
    nodeClass: DataValidateNode
  },
  {
    type: 'data/filter',
    name: '数据过滤',
    description: '过滤数据数组',
    category: NodeCategory.DATA,
    icon: 'filter_list',
    color: '#4CAF50',
    tags: ['data', 'filter', 'array', 'batch4'],
    inputs: [
      { name: 'data', label: '数据', type: DataType.ARRAY, direction: 'input', required: true },
      { name: 'condition', label: '条件', type: DataType.STRING, direction: 'input', required: false },
      { name: 'property', label: '属性', type: DataType.STRING, direction: 'input', required: false },
      { name: 'value', label: '值', type: DataType.ANY, direction: 'input', required: false },
      { name: 'operator', label: '操作符', type: DataType.STRING, direction: 'input', required: false, defaultValue: 'equals' }
    ],
    outputs: [
      { name: 'filtered', label: '过滤结果', type: DataType.ARRAY, direction: 'output', required: false },
      { name: 'count', label: '数量', type: DataType.NUMBER, direction: 'output', required: false }
    ],
    nodeClass: DataFilterNode
  }
];

/**
 * 注册第四批次的所有节点
 */
export function registerBatch4Nodes(): void {
  console.log('开始注册第四批次：数学计算和数据流节点...');

  const nodeRegistry = NodeRegistry.getInstance();

  // 注册高级变换节点 (151-160)
  nodeRegistry.registerBatch(advancedTransformNodeDefinitions);

  // 注册高级数学节点 (161-180)
  nodeRegistry.registerBatch(advancedMathNodeDefinitions);

  // 注册数据流节点 (181-200)
  nodeRegistry.registerBatch(dataFlowNodeDefinitions);

  console.log('第四批次节点注册完成！');

  // 输出统计信息
  const stats = nodeRegistry.getStats();
  console.log(`总计注册节点: ${stats.totalNodes} 个`);
  console.log('按分类统计:', Object.fromEntries(stats.byCategory));
}

/**
 * 导出所有节点定义
 */
export {
  advancedTransformNodeDefinitions,
  advancedMathNodeDefinitions,
  dataFlowNodeDefinitions
};
