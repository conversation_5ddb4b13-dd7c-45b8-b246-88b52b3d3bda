/**
 * 第五批次：相机系统节点 (221-240)
 * 实现相机系统功能节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 221 - 相机创建节点
 * 创建新的相机实例
 */
export class CameraCreateNode extends BaseNode {
  public static readonly TYPE = 'CameraCreate';
  public static readonly TITLE = '相机创建';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = CameraCreateNode.TITLE;
    
    // 输入端口
    this.addInputPort('type', DataType.STRING, '相机类型');
    this.addInputPort('fov', DataType.NUMBER, '视野角度');
    this.addInputPort('aspect', DataType.NUMBER, '宽高比');
    this.addInputPort('near', DataType.NUMBER, '近平面');
    this.addInputPort('far', DataType.NUMBER, '远平面');
    
    // 输出端口
    this.addOutputPort('camera', DataType.OBJECT, '相机对象');
    this.addOutputPort('type', DataType.STRING, '相机类型');
    
    // 属性
    this.addProperty('defaultFov', DataType.NUMBER, 75, '默认视野角度');
    this.addProperty('defaultNear', DataType.NUMBER, 0.1, '默认近平面');
    this.addProperty('defaultFar', DataType.NUMBER, 1000, '默认远平面');
    this.addProperty('autoAspect', DataType.BOOLEAN, true, '自动宽高比');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const type = this.getInputValue('type') || 'PerspectiveCamera';
      const fov = this.getInputValue('fov') || this.getProperty('defaultFov');
      const near = this.getInputValue('near') || this.getProperty('defaultNear');
      const far = this.getInputValue('far') || this.getProperty('defaultFar');
      
      let aspect = this.getInputValue('aspect');
      if (!aspect && this.getProperty('autoAspect')) {
        aspect = window.innerWidth / window.innerHeight;
      }
      
      let camera;
      
      switch (type) {
        case 'PerspectiveCamera':
          camera = new THREE.PerspectiveCamera(fov, aspect, near, far);
          break;
          
        case 'OrthographicCamera':
          const width = aspect * 10; // 默认正交相机宽度
          const height = 10;
          camera = new THREE.OrthographicCamera(
            -width / 2, width / 2,
            height / 2, -height / 2,
            near, far
          );
          break;
          
        case 'ArrayCamera':
          camera = new THREE.ArrayCamera();
          break;
          
        case 'CubeCamera':
          camera = new THREE.CubeCamera(near, far, new THREE.WebGLCubeRenderTarget(512));
          break;
          
        default:
          camera = new THREE.PerspectiveCamera(fov, aspect, near, far);
      }
      
      // 设置默认位置
      camera.position.set(0, 0, 5);
      camera.lookAt(0, 0, 0);
      
      this.setOutputValue('camera', camera);
      this.setOutputValue('type', type);
      
    } catch (error) {
      console.error('相机创建失败:', error);
      throw error;
    }
  }
}

/**
 * 222 - 相机切换节点
 * 切换活跃相机
 */
export class CameraSwitchNode extends BaseNode {
  public static readonly TYPE = 'CameraSwitch';
  public static readonly TITLE = '相机切换';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  private cameras: Map<string, THREE.Camera> = new Map();
  private activeCamera: THREE.Camera | null = null;

  constructor() {
    super();
    this.title = CameraSwitchNode.TITLE;
    
    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机对象');
    this.addInputPort('cameraId', DataType.STRING, '相机ID');
    this.addInputPort('activate', DataType.BOOLEAN, '激活相机');
    
    // 输出端口
    this.addOutputPort('activeCamera', DataType.OBJECT, '活跃相机');
    this.addOutputPort('previousCamera', DataType.OBJECT, '前一个相机');
    this.addOutputPort('cameraList', DataType.ARRAY, '相机列表');
    
    // 属性
    this.addProperty('smoothTransition', DataType.BOOLEAN, true, '平滑过渡');
    this.addProperty('transitionDuration', DataType.NUMBER, 1.0, '过渡时间');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const camera = this.getInputValue('camera');
      const cameraId = this.getInputValue('cameraId') || 'default';
      const activate = this.getInputValue('activate');
      
      // 注册相机
      if (camera) {
        this.cameras.set(cameraId, camera);
      }
      
      // 激活相机
      if (activate && this.cameras.has(cameraId)) {
        const previousCamera = this.activeCamera;
        this.activeCamera = this.cameras.get(cameraId);
        
        // 平滑过渡
        if (this.getProperty('smoothTransition') && previousCamera && this.activeCamera) {
          this.performSmoothTransition(previousCamera, this.activeCamera);
        }
        
        this.setOutputValue('previousCamera', previousCamera);
      }
      
      this.setOutputValue('activeCamera', this.activeCamera);
      this.setOutputValue('cameraList', Array.from(this.cameras.values()));
      
    } catch (error) {
      console.error('相机切换失败:', error);
      throw error;
    }
  }

  private performSmoothTransition(from: THREE.Camera, to: THREE.Camera): void {
    const duration = this.getProperty('transitionDuration') * 1000;
    const startTime = Date.now();
    
    const startPosition = from.position.clone();
    const startRotation = from.quaternion.clone();
    const endPosition = to.position.clone();
    const endRotation = to.quaternion.clone();
    
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 使用缓动函数
      const eased = this.easeInOutCubic(progress);
      
      // 插值位置和旋转
      from.position.lerpVectors(startPosition, endPosition, eased);
      from.quaternion.slerpQuaternions(startRotation, endRotation, eased);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    animate();
  }

  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }
}

/**
 * 223 - 透视相机节点
 * 透视投影相机设置
 */
export class PerspectiveCameraNode extends BaseNode {
  public static readonly TYPE = 'PerspectiveCamera';
  public static readonly TITLE = '透视相机';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = PerspectiveCameraNode.TITLE;
    
    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机对象');
    this.addInputPort('fov', DataType.NUMBER, '视野角度');
    this.addInputPort('aspect', DataType.NUMBER, '宽高比');
    this.addInputPort('near', DataType.NUMBER, '近平面');
    this.addInputPort('far', DataType.NUMBER, '远平面');
    
    // 输出端口
    this.addOutputPort('perspectiveCamera', DataType.OBJECT, '透视相机');
    this.addOutputPort('projectionMatrix', DataType.OBJECT, '投影矩阵');
    
    // 属性
    this.addProperty('filmGauge', DataType.NUMBER, 35, '胶片规格');
    this.addProperty('filmOffset', DataType.NUMBER, 0, '胶片偏移');
    this.addProperty('focus', DataType.NUMBER, 10, '焦点距离');
    this.addProperty('zoom', DataType.NUMBER, 1, '缩放系数');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      let camera = this.getInputValue('camera');
      const fov = this.getInputValue('fov');
      const aspect = this.getInputValue('aspect');
      const near = this.getInputValue('near');
      const far = this.getInputValue('far');
      
      // 如果没有提供相机，创建新的透视相机
      if (!camera) {
        camera = new THREE.PerspectiveCamera(
          fov || 75,
          aspect || (window.innerWidth / window.innerHeight),
          near || 0.1,
          far || 1000
        );
      }
      
      // 确保是透视相机
      if (!(camera instanceof THREE.PerspectiveCamera)) {
        throw new Error('需要透视相机对象');
      }
      
      // 更新相机参数
      if (fov !== undefined) camera.fov = fov;
      if (aspect !== undefined) camera.aspect = aspect;
      if (near !== undefined) camera.near = near;
      if (far !== undefined) camera.far = far;
      
      // 设置高级属性
      camera.filmGauge = this.getProperty('filmGauge');
      camera.filmOffset = this.getProperty('filmOffset');
      camera.focus = this.getProperty('focus');
      camera.zoom = this.getProperty('zoom');
      
      // 更新投影矩阵
      camera.updateProjectionMatrix();
      
      this.setOutputValue('perspectiveCamera', camera);
      this.setOutputValue('projectionMatrix', camera.projectionMatrix);
      
    } catch (error) {
      console.error('透视相机设置失败:', error);
      throw error;
    }
  }
}

/**
 * 224 - 正交相机节点
 * 正交投影相机设置
 */
export class OrthographicCameraNode extends BaseNode {
  public static readonly TYPE = 'OrthographicCamera';
  public static readonly TITLE = '正交相机';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = OrthographicCameraNode.TITLE;
    
    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机对象');
    this.addInputPort('left', DataType.NUMBER, '左边界');
    this.addInputPort('right', DataType.NUMBER, '右边界');
    this.addInputPort('top', DataType.NUMBER, '上边界');
    this.addInputPort('bottom', DataType.NUMBER, '下边界');
    this.addInputPort('near', DataType.NUMBER, '近平面');
    this.addInputPort('far', DataType.NUMBER, '远平面');
    
    // 输出端口
    this.addOutputPort('orthographicCamera', DataType.OBJECT, '正交相机');
    this.addOutputPort('projectionMatrix', DataType.OBJECT, '投影矩阵');
    
    // 属性
    this.addProperty('zoom', DataType.NUMBER, 1, '缩放系数');
    this.addProperty('view', DataType.OBJECT, null, '视图设置');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      let camera = this.getInputValue('camera');
      const left = this.getInputValue('left');
      const right = this.getInputValue('right');
      const top = this.getInputValue('top');
      const bottom = this.getInputValue('bottom');
      const near = this.getInputValue('near');
      const far = this.getInputValue('far');
      
      // 如果没有提供相机，创建新的正交相机
      if (!camera) {
        camera = new THREE.OrthographicCamera(
          left || -10, right || 10,
          top || 10, bottom || -10,
          near || 0.1, far || 1000
        );
      }
      
      // 确保是正交相机
      if (!(camera instanceof THREE.OrthographicCamera)) {
        throw new Error('需要正交相机对象');
      }
      
      // 更新相机参数
      if (left !== undefined) camera.left = left;
      if (right !== undefined) camera.right = right;
      if (top !== undefined) camera.top = top;
      if (bottom !== undefined) camera.bottom = bottom;
      if (near !== undefined) camera.near = near;
      if (far !== undefined) camera.far = far;
      
      // 设置缩放
      camera.zoom = this.getProperty('zoom');
      
      // 设置视图
      const view = this.getProperty('view');
      if (view) {
        camera.setViewOffset(
          view.fullWidth || window.innerWidth,
          view.fullHeight || window.innerHeight,
          view.x || 0,
          view.y || 0,
          view.width || window.innerWidth,
          view.height || window.innerHeight
        );
      }
      
      // 更新投影矩阵
      camera.updateProjectionMatrix();
      
      this.setOutputValue('orthographicCamera', camera);
      this.setOutputValue('projectionMatrix', camera.projectionMatrix);
      
    } catch (error) {
      console.error('正交相机设置失败:', error);
      throw error;
    }
  }
}

/**
 * 225 - 相机位置节点
 * 相机位置控制
 */
export class CameraPositionNode extends BaseNode {
  public static readonly TYPE = 'CameraPosition';
  public static readonly TITLE = '相机位置';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = CameraPositionNode.TITLE;

    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机对象');
    this.addInputPort('position', DataType.OBJECT, '位置向量');
    this.addInputPort('x', DataType.NUMBER, 'X坐标');
    this.addInputPort('y', DataType.NUMBER, 'Y坐标');
    this.addInputPort('z', DataType.NUMBER, 'Z坐标');

    // 输出端口
    this.addOutputPort('camera', DataType.OBJECT, '相机对象');
    this.addOutputPort('position', DataType.OBJECT, '当前位置');

    // 属性
    this.addProperty('smooth', DataType.BOOLEAN, false, '平滑移动');
    this.addProperty('speed', DataType.NUMBER, 1.0, '移动速度');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const camera = this.getInputValue('camera');
      const position = this.getInputValue('position');
      const x = this.getInputValue('x');
      const y = this.getInputValue('y');
      const z = this.getInputValue('z');

      if (!camera) {
        throw new Error('需要相机对象');
      }

      let targetPosition;

      if (position) {
        targetPosition = position;
      } else {
        targetPosition = new THREE.Vector3(
          x !== undefined ? x : camera.position.x,
          y !== undefined ? y : camera.position.y,
          z !== undefined ? z : camera.position.z
        );
      }

      if (this.getProperty('smooth')) {
        // 平滑移动
        const speed = this.getProperty('speed');
        camera.position.lerp(targetPosition, speed * 0.1);
      } else {
        // 直接设置位置
        camera.position.copy(targetPosition);
      }

      this.setOutputValue('camera', camera);
      this.setOutputValue('position', camera.position.clone());

    } catch (error) {
      console.error('相机位置设置失败:', error);
      throw error;
    }
  }
}

/**
 * 226 - 相机目标节点
 * 相机注视目标
 */
export class CameraTargetNode extends BaseNode {
  public static readonly TYPE = 'CameraTarget';
  public static readonly TITLE = '相机目标';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = CameraTargetNode.TITLE;

    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机对象');
    this.addInputPort('target', DataType.OBJECT, '目标位置');
    this.addInputPort('targetObject', DataType.OBJECT, '目标对象');
    this.addInputPort('up', DataType.OBJECT, '上方向');

    // 输出端口
    this.addOutputPort('camera', DataType.OBJECT, '相机对象');
    this.addOutputPort('direction', DataType.OBJECT, '观察方向');

    // 属性
    this.addProperty('smooth', DataType.BOOLEAN, false, '平滑转向');
    this.addProperty('speed', DataType.NUMBER, 1.0, '转向速度');
    this.addProperty('autoUpdate', DataType.BOOLEAN, true, '自动更新');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const camera = this.getInputValue('camera');
      const target = this.getInputValue('target');
      const targetObject = this.getInputValue('targetObject');
      const up = this.getInputValue('up');

      if (!camera) {
        throw new Error('需要相机对象');
      }

      let lookAtTarget;

      if (targetObject && targetObject.position) {
        lookAtTarget = targetObject.position;
      } else if (target) {
        lookAtTarget = target;
      } else {
        lookAtTarget = new THREE.Vector3(0, 0, 0);
      }

      if (this.getProperty('smooth')) {
        // 平滑转向
        const direction = new THREE.Vector3()
          .subVectors(lookAtTarget, camera.position)
          .normalize();

        const currentDirection = new THREE.Vector3(0, 0, -1)
          .applyQuaternion(camera.quaternion);

        const speed = this.getProperty('speed');
        const newDirection = currentDirection.lerp(direction, speed * 0.1);

        const lookAtMatrix = new THREE.Matrix4()
          .lookAt(camera.position, camera.position.clone().add(newDirection), up || camera.up);

        camera.quaternion.setFromRotationMatrix(lookAtMatrix);
      } else {
        // 直接看向目标
        camera.lookAt(lookAtTarget);
        if (up) {
          camera.up.copy(up);
        }
      }

      const direction = new THREE.Vector3()
        .subVectors(lookAtTarget, camera.position)
        .normalize();

      this.setOutputValue('camera', camera);
      this.setOutputValue('direction', direction);

    } catch (error) {
      console.error('相机目标设置失败:', error);
      throw error;
    }
  }
}

/**
 * 227 - 相机跟随节点
 * 相机跟随目标
 */
export class CameraFollowNode extends BaseNode {
  public static readonly TYPE = 'CameraFollow';
  public static readonly TITLE = '相机跟随';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  private lastTargetPosition = new THREE.Vector3();

  constructor() {
    super();
    this.title = CameraFollowNode.TITLE;

    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机对象');
    this.addInputPort('target', DataType.OBJECT, '跟随目标');
    this.addInputPort('offset', DataType.OBJECT, '位置偏移');
    this.addInputPort('enabled', DataType.BOOLEAN, '启用跟随');

    // 输出端口
    this.addOutputPort('camera', DataType.OBJECT, '相机对象');
    this.addOutputPort('distance', DataType.NUMBER, '距离目标距离');

    // 属性
    this.addProperty('followSpeed', DataType.NUMBER, 0.1, '跟随速度');
    this.addProperty('lookAtTarget', DataType.BOOLEAN, true, '看向目标');
    this.addProperty('damping', DataType.NUMBER, 0.05, '阻尼系数');
    this.addProperty('maxDistance', DataType.NUMBER, 50, '最大距离');
    this.addProperty('minDistance', DataType.NUMBER, 1, '最小距离');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const camera = this.getInputValue('camera');
      const target = this.getInputValue('target');
      const offset = this.getInputValue('offset') || new THREE.Vector3(0, 5, 10);
      const enabled = this.getInputValue('enabled') !== false;

      if (!camera || !target || !enabled) {
        this.setOutputValue('camera', camera);
        this.setOutputValue('distance', 0);
        return;
      }

      const targetPosition = target.position || target;

      // 计算目标位置（包含偏移）
      const desiredPosition = targetPosition.clone().add(offset);

      // 应用距离限制
      const distance = camera.position.distanceTo(targetPosition);
      const maxDistance = this.getProperty('maxDistance');
      const minDistance = this.getProperty('minDistance');

      if (distance > maxDistance) {
        const direction = new THREE.Vector3()
          .subVectors(camera.position, targetPosition)
          .normalize()
          .multiplyScalar(maxDistance);
        desiredPosition.copy(targetPosition).add(direction);
      } else if (distance < minDistance) {
        const direction = new THREE.Vector3()
          .subVectors(camera.position, targetPosition)
          .normalize()
          .multiplyScalar(minDistance);
        desiredPosition.copy(targetPosition).add(direction);
      }

      // 平滑跟随
      const followSpeed = this.getProperty('followSpeed');
      const damping = this.getProperty('damping');

      // 计算速度
      const velocity = new THREE.Vector3()
        .subVectors(targetPosition, this.lastTargetPosition)
        .multiplyScalar(damping);

      // 预测位置
      const predictedPosition = desiredPosition.clone().add(velocity);

      // 插值到目标位置
      camera.position.lerp(predictedPosition, followSpeed);

      // 看向目标
      if (this.getProperty('lookAtTarget')) {
        camera.lookAt(targetPosition);
      }

      // 更新上一帧目标位置
      this.lastTargetPosition.copy(targetPosition);

      const currentDistance = camera.position.distanceTo(targetPosition);

      this.setOutputValue('camera', camera);
      this.setOutputValue('distance', currentDistance);

    } catch (error) {
      console.error('相机跟随失败:', error);
      throw error;
    }
  }
}
