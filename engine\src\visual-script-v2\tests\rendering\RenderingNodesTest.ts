/**
 * 渲染系统节点测试
 * 测试第五批次渲染系统节点的功能和集成
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { MockExecutionContext } from '../mocks/MockExecutionContext';
import { NodeCategory, DataType } from '../../core/types';

// 导入基础渲染节点
import {
  RendererInitializeNode,
  RenderTargetNode,
  ViewportSetNode,
  ClearScreenNode,
  RenderQueueNode
} from '../../nodes/categories/rendering/BasicRenderingNodes';

// 导入相机系统节点
import {
  CameraCreateNode,
  CameraSwitchNode,
  PerspectiveCameraNode,
  CameraPositionNode
} from '../../nodes/categories/rendering/CameraSystemNodes';

// 导入光照系统节点
import {
  AmbientLightNode,
  DirectionalLightNode,
  PointLightNode,
  LightIntensityNode
} from '../../nodes/categories/rendering/LightingSystemNodes';

// 导入注册表和集成
import { registerBatch5RenderingNodes } from '../../nodes/registry/Batch5RenderingNodesRegistry';
import { RenderingNodesIntegration } from '../../editor/integration/RenderingNodesIntegration';

// Mock THREE.js
const mockTHREE = {
  WebGLRenderer: jest.fn().mockImplementation(() => ({
    setSize: jest.fn(),
    setPixelRatio: jest.fn(),
    shadowMap: { enabled: false, type: 'PCFSoftShadowMap' },
    setClearColor: jest.fn(),
    clear: jest.fn(),
    setViewport: jest.fn()
  })),
  WebGLRenderTarget: jest.fn().mockImplementation(() => ({
    texture: {},
    depthTexture: null
  })),
  PerspectiveCamera: jest.fn().mockImplementation(() => ({
    position: { x: 0, y: 0, z: 5, copy: jest.fn(), clone: jest.fn() },
    lookAt: jest.fn(),
    updateProjectionMatrix: jest.fn(),
    fov: 75,
    aspect: 1,
    near: 0.1,
    far: 1000
  })),
  OrthographicCamera: jest.fn().mockImplementation(() => ({
    position: { x: 0, y: 0, z: 5, copy: jest.fn(), clone: jest.fn() },
    lookAt: jest.fn(),
    updateProjectionMatrix: jest.fn(),
    left: -10,
    right: 10,
    top: 10,
    bottom: -10,
    near: 0.1,
    far: 1000
  })),
  AmbientLight: jest.fn().mockImplementation(() => ({
    color: { r: 1, g: 1, b: 1 },
    intensity: 0.5,
    visible: true
  })),
  DirectionalLight: jest.fn().mockImplementation(() => ({
    color: { r: 1, g: 1, b: 1 },
    intensity: 1.0,
    position: { set: jest.fn(), copy: jest.fn() },
    target: { position: { copy: jest.fn() } },
    castShadow: false,
    shadow: {
      mapSize: { width: 1024, height: 1024 },
      camera: { near: 0.5, far: 500, left: -50, right: 50, top: 50, bottom: -50 }
    }
  })),
  PointLight: jest.fn().mockImplementation(() => ({
    color: { r: 1, g: 1, b: 1 },
    intensity: 1.0,
    position: { copy: jest.fn() },
    castShadow: false,
    shadow: {
      mapSize: { width: 512, height: 512 },
      camera: { near: 0.1, far: 100 }
    }
  })),
  Color: jest.fn().mockImplementation(() => ({
    r: 1, g: 1, b: 1,
    copy: jest.fn(),
    clone: jest.fn(),
    lerp: jest.fn()
  })),
  Vector3: jest.fn().mockImplementation(() => ({
    x: 0, y: 0, z: 0,
    set: jest.fn(),
    copy: jest.fn(),
    clone: jest.fn(),
    add: jest.fn(),
    lerp: jest.fn()
  })),
  RGBAFormat: 'RGBAFormat',
  UnsignedByteType: 'UnsignedByteType',
  PCFSoftShadowMap: 'PCFSoftShadowMap'
};

// 设置全局 THREE
(global as any).THREE = mockTHREE;
(global as any).window = {
  innerWidth: 1920,
  innerHeight: 1080,
  devicePixelRatio: 1
};

describe('渲染系统节点测试', () => {
  let mockContext: MockExecutionContext;

  beforeEach(() => {
    mockContext = new MockExecutionContext();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('基础渲染节点测试', () => {
    test('渲染器初始化节点应该正确创建渲染器', async () => {
      const node = new RendererInitializeNode();
      
      // 设置输入
      node.setInputValue('canvas', document.createElement('canvas'));
      node.setInputValue('config', { antialias: true });
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证输出
      const renderer = node.getOutputValue('renderer');
      const success = node.getOutputValue('success');
      
      expect(renderer).toBeDefined();
      expect(success).toBe(true);
      expect(mockTHREE.WebGLRenderer).toHaveBeenCalled();
    });

    test('渲染目标节点应该创建渲染目标', async () => {
      const node = new RenderTargetNode();
      const mockRenderer = new mockTHREE.WebGLRenderer();
      
      // 设置输入
      node.setInputValue('renderer', mockRenderer);
      node.setInputValue('width', 512);
      node.setInputValue('height', 512);
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证输出
      const renderTarget = node.getOutputValue('renderTarget');
      const texture = node.getOutputValue('texture');
      
      expect(renderTarget).toBeDefined();
      expect(texture).toBeDefined();
      expect(mockTHREE.WebGLRenderTarget).toHaveBeenCalledWith(512, 512, expect.any(Object));
    });

    test('视口设置节点应该设置渲染视口', async () => {
      const node = new ViewportSetNode();
      const mockRenderer = {
        setViewport: jest.fn(),
        setPixelRatio: jest.fn(),
        setSize: jest.fn()
      };
      
      // 设置输入
      node.setInputValue('renderer', mockRenderer);
      node.setInputValue('x', 0);
      node.setInputValue('y', 0);
      node.setInputValue('width', 800);
      node.setInputValue('height', 600);
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证调用
      expect(mockRenderer.setViewport).toHaveBeenCalledWith(0, 0, 800, 600);
      
      // 验证输出
      const viewport = node.getOutputValue('viewport');
      expect(viewport).toEqual({
        x: 0, y: 0, width: 800, height: 600, pixelRatio: 1
      });
    });

    test('清屏操作节点应该清理屏幕', async () => {
      const node = new ClearScreenNode();
      const mockRenderer = {
        setClearColor: jest.fn(),
        clear: jest.fn()
      };
      
      // 设置输入
      node.setInputValue('renderer', mockRenderer);
      node.setInputValue('color', new mockTHREE.Color());
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证调用
      expect(mockRenderer.setClearColor).toHaveBeenCalled();
      expect(mockRenderer.clear).toHaveBeenCalled();
      
      // 验证输出
      const cleared = node.getOutputValue('cleared');
      expect(cleared).toBe(true);
    });

    test('渲染队列节点应该管理渲染队列', async () => {
      const node = new RenderQueueNode();
      const mockObject = { id: 'test-object' };
      
      // 设置输入
      node.setInputValue('object', mockObject);
      node.setInputValue('priority', 10);
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证输出
      const queue = node.getOutputValue('queue');
      const count = node.getOutputValue('count');
      
      expect(queue).toHaveLength(1);
      expect(count).toBe(1);
      expect(queue[0]).toMatchObject({
        object: mockObject,
        priority: 10
      });
    });
  });

  describe('相机系统节点测试', () => {
    test('相机创建节点应该创建透视相机', async () => {
      const node = new CameraCreateNode();
      
      // 设置输入
      node.setInputValue('type', 'PerspectiveCamera');
      node.setInputValue('fov', 60);
      node.setInputValue('aspect', 16/9);
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证输出
      const camera = node.getOutputValue('camera');
      const type = node.getOutputValue('type');
      
      expect(camera).toBeDefined();
      expect(type).toBe('PerspectiveCamera');
      expect(mockTHREE.PerspectiveCamera).toHaveBeenCalledWith(60, 16/9, 0.1, 1000);
    });

    test('透视相机节点应该配置透视相机参数', async () => {
      const node = new PerspectiveCameraNode();
      const mockCamera = new mockTHREE.PerspectiveCamera();
      
      // 设置输入
      node.setInputValue('camera', mockCamera);
      node.setInputValue('fov', 90);
      node.setInputValue('near', 0.5);
      node.setInputValue('far', 2000);
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证相机参数
      expect(mockCamera.fov).toBe(90);
      expect(mockCamera.near).toBe(0.5);
      expect(mockCamera.far).toBe(2000);
      expect(mockCamera.updateProjectionMatrix).toHaveBeenCalled();
    });

    test('相机位置节点应该设置相机位置', async () => {
      const node = new CameraPositionNode();
      const mockCamera = {
        position: {
          x: 0, y: 0, z: 0,
          copy: jest.fn(),
          clone: jest.fn().mockReturnValue({ x: 10, y: 5, z: 15 }),
          lerp: jest.fn()
        }
      };
      
      // 设置输入
      node.setInputValue('camera', mockCamera);
      node.setInputValue('x', 10);
      node.setInputValue('y', 5);
      node.setInputValue('z', 15);
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证输出
      const camera = node.getOutputValue('camera');
      const position = node.getOutputValue('position');
      
      expect(camera).toBe(mockCamera);
      expect(position).toEqual({ x: 10, y: 5, z: 15 });
    });
  });

  describe('光照系统节点测试', () => {
    test('环境光节点应该创建环境光', async () => {
      const node = new AmbientLightNode();
      const mockColor = new mockTHREE.Color();
      
      // 设置输入
      node.setInputValue('color', mockColor);
      node.setInputValue('intensity', 0.8);
      node.setInputValue('enabled', true);
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证输出
      const light = node.getOutputValue('light');
      const color = node.getOutputValue('color');
      const intensity = node.getOutputValue('intensity');
      
      expect(light).toBeDefined();
      expect(mockTHREE.AmbientLight).toHaveBeenCalledWith(mockColor, 0.8);
      expect(light.visible).toBe(true);
    });

    test('方向光节点应该创建方向光', async () => {
      const node = new DirectionalLightNode();
      const mockColor = new mockTHREE.Color();
      
      // 设置输入
      node.setInputValue('color', mockColor);
      node.setInputValue('intensity', 1.5);
      node.setInputValue('castShadow', true);
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证输出
      const light = node.getOutputValue('light');
      const target = node.getOutputValue('target');
      const shadow = node.getOutputValue('shadow');
      
      expect(light).toBeDefined();
      expect(target).toBeDefined();
      expect(shadow).toBeDefined();
      expect(mockTHREE.DirectionalLight).toHaveBeenCalledWith(mockColor, 1.5);
      expect(light.castShadow).toBe(true);
    });

    test('点光源节点应该创建点光源', async () => {
      const node = new PointLightNode();
      const mockPosition = new mockTHREE.Vector3();
      
      // 设置输入
      node.setInputValue('intensity', 2.0);
      node.setInputValue('distance', 50);
      node.setInputValue('position', mockPosition);
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证输出
      const light = node.getOutputValue('light');
      
      expect(light).toBeDefined();
      expect(mockTHREE.PointLight).toHaveBeenCalled();
      expect(light.position.copy).toHaveBeenCalledWith(mockPosition);
    });

    test('光照强度节点应该调节光源强度', async () => {
      const node = new LightIntensityNode();
      const mockLight = {
        intensity: 1.0
      };
      
      // 设置输入
      node.setInputValue('light', mockLight);
      node.setInputValue('intensity', 2.5);
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证输出
      const light = node.getOutputValue('light');
      const currentIntensity = node.getOutputValue('currentIntensity');
      
      expect(light).toBe(mockLight);
      expect(mockLight.intensity).toBe(2.5);
      expect(currentIntensity).toBe(2.5);
    });
  });

  describe('节点注册和集成测试', () => {
    test('应该成功注册所有渲染系统节点', () => {
      // 这个测试需要模拟 NodeRegistry
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      expect(() => {
        registerBatch5RenderingNodes();
      }).not.toThrow();
      
      expect(consoleSpy).toHaveBeenCalledWith('开始注册第五批次：基础渲染系统节点...');
      expect(consoleSpy).toHaveBeenCalledWith('第五批次渲染系统节点注册完成！');
      
      consoleSpy.mockRestore();
    });

    test('渲染节点集成应该正确初始化', async () => {
      const mockEditor = {
        getNodePalette: jest.fn().mockReturnValue({
          addNodeGroup: jest.fn(),
          enableDragToCanvas: jest.fn(),
          setDragPreview: jest.fn(),
          onNodeDragStart: jest.fn(),
          onNodeDragEnd: jest.fn(),
          enableHoverPreview: jest.fn(),
          setPreviewProvider: jest.fn(),
          removeAllListeners: jest.fn()
        }),
        setDragMode: jest.fn(),
        createNodeAt: jest.fn(),
        createNode: jest.fn(),
        addKeyboardShortcut: jest.fn(),
        loadTemplate: jest.fn(),
        clearKeyboardShortcuts: jest.fn()
      };
      
      const integration = new RenderingNodesIntegration(mockEditor as any);
      
      await expect(integration.initialize()).resolves.not.toThrow();
      
      // 验证统计信息
      const stats = integration.getStats();
      expect(stats.totalNodes).toBe(41);
      expect(stats.basicRenderingNodes).toBe(20);
      expect(stats.cameraSystemNodes).toBe(11);
      expect(stats.lightingSystemNodes).toBe(10);
    });
  });

  describe('错误处理测试', () => {
    test('渲染器初始化节点应该处理错误', async () => {
      const node = new RendererInitializeNode();
      
      // 模拟错误
      mockTHREE.WebGLRenderer.mockImplementationOnce(() => {
        throw new Error('WebGL not supported');
      });
      
      // 执行节点
      await node.execute(mockContext);
      
      // 验证错误处理
      const success = node.getOutputValue('success');
      const error = node.getOutputValue('error');
      const renderer = node.getOutputValue('renderer');
      
      expect(success).toBe(false);
      expect(error).toBe('WebGL not supported');
      expect(renderer).toBeNull();
    });

    test('相机创建节点应该处理无效类型', async () => {
      const node = new CameraCreateNode();
      
      // 设置无效类型
      node.setInputValue('type', 'InvalidCamera');
      
      // 执行节点应该回退到默认透视相机
      await node.execute(mockContext);
      
      const camera = node.getOutputValue('camera');
      expect(camera).toBeDefined();
      expect(mockTHREE.PerspectiveCamera).toHaveBeenCalled();
    });
  });
});
