/**
 * 第五批次：基础渲染系统节点 (208-220) - 第二部分
 * 继续实现基础渲染功能节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 208 - 视锥剔除节点
 * 视锥体剔除优化
 */
export class FrustumCullingNode extends BaseNode {
  public static readonly TYPE = 'FrustumCulling';
  public static readonly TITLE = '视锥剔除';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = FrustumCullingNode.TITLE;
    
    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机');
    this.addInputPort('objects', DataType.ARRAY, '对象数组');
    this.addInputPort('frustum', DataType.OBJECT, '视锥体');
    
    // 输出端口
    this.addOutputPort('visibleObjects', DataType.ARRAY, '可见对象');
    this.addOutputPort('culledObjects', DataType.ARRAY, '被剔除对象');
    this.addOutputPort('visibleCount', DataType.NUMBER, '可见对象数量');
    this.addOutputPort('culledCount', DataType.NUMBER, '剔除对象数量');
    
    // 属性
    this.addProperty('enabled', DataType.BOOLEAN, true, '启用剔除');
    this.addProperty('margin', DataType.NUMBER, 0, '剔除边距');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const camera = this.getInputValue('camera');
      const objects = this.getInputValue('objects') || [];
      let frustum = this.getInputValue('frustum');
      
      if (!this.getProperty('enabled')) {
        this.setOutputValue('visibleObjects', objects);
        this.setOutputValue('culledObjects', []);
        this.setOutputValue('visibleCount', objects.length);
        this.setOutputValue('culledCount', 0);
        return;
      }
      
      // 如果没有提供视锥体，从相机创建
      if (!frustum && camera) {
        frustum = new THREE.Frustum();
        const matrix = new THREE.Matrix4().multiplyMatrices(
          camera.projectionMatrix,
          camera.matrixWorldInverse
        );
        frustum.setFromProjectionMatrix(matrix);
      }
      
      if (!frustum) {
        throw new Error('需要相机或视锥体');
      }
      
      const visibleObjects = [];
      const culledObjects = [];
      const margin = this.getProperty('margin');
      
      for (const obj of objects) {
        if (obj.geometry && obj.geometry.boundingSphere) {
          // 扩展包围球半径
          const sphere = obj.geometry.boundingSphere.clone();
          sphere.radius += margin;
          
          // 变换到世界坐标
          sphere.applyMatrix4(obj.matrixWorld);
          
          if (frustum.intersectsSphere(sphere)) {
            visibleObjects.push(obj);
          } else {
            culledObjects.push(obj);
          }
        } else {
          // 没有包围球信息，默认可见
          visibleObjects.push(obj);
        }
      }
      
      this.setOutputValue('visibleObjects', visibleObjects);
      this.setOutputValue('culledObjects', culledObjects);
      this.setOutputValue('visibleCount', visibleObjects.length);
      this.setOutputValue('culledCount', culledObjects.length);
      
    } catch (error) {
      console.error('视锥剔除失败:', error);
      throw error;
    }
  }
}

/**
 * 209 - 遮挡剔除节点
 * 遮挡剔除优化
 */
export class OcclusionCullingNode extends BaseNode {
  public static readonly TYPE = 'OcclusionCulling';
  public static readonly TITLE = '遮挡剔除';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = OcclusionCullingNode.TITLE;
    
    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机');
    this.addInputPort('objects', DataType.ARRAY, '对象数组');
    this.addInputPort('occluders', DataType.ARRAY, '遮挡物数组');
    
    // 输出端口
    this.addOutputPort('visibleObjects', DataType.ARRAY, '可见对象');
    this.addOutputPort('occludedObjects', DataType.ARRAY, '被遮挡对象');
    this.addOutputPort('visibleCount', DataType.NUMBER, '可见对象数量');
    
    // 属性
    this.addProperty('enabled', DataType.BOOLEAN, true, '启用遮挡剔除');
    this.addProperty('threshold', DataType.NUMBER, 0.1, '遮挡阈值');
    this.addProperty('maxDistance', DataType.NUMBER, 100, '最大检测距离');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const camera = this.getInputValue('camera');
      const objects = this.getInputValue('objects') || [];
      const occluders = this.getInputValue('occluders') || [];
      
      if (!this.getProperty('enabled') || !camera) {
        this.setOutputValue('visibleObjects', objects);
        this.setOutputValue('occludedObjects', []);
        this.setOutputValue('visibleCount', objects.length);
        return;
      }
      
      const visibleObjects = [];
      const occludedObjects = [];
      const raycaster = new THREE.Raycaster();
      const maxDistance = this.getProperty('maxDistance');
      
      for (const obj of objects) {
        if (!obj.position) continue;
        
        // 计算从相机到对象的方向
        const direction = new THREE.Vector3()
          .subVectors(obj.position, camera.position)
          .normalize();
        
        const distance = camera.position.distanceTo(obj.position);
        
        if (distance > maxDistance) {
          occludedObjects.push(obj);
          continue;
        }
        
        // 设置射线
        raycaster.set(camera.position, direction);
        raycaster.far = distance;
        
        // 检测与遮挡物的交集
        const intersections = raycaster.intersectObjects(occluders, true);
        
        let isOccluded = false;
        for (const intersection of intersections) {
          if (intersection.distance < distance - this.getProperty('threshold')) {
            isOccluded = true;
            break;
          }
        }
        
        if (isOccluded) {
          occludedObjects.push(obj);
        } else {
          visibleObjects.push(obj);
        }
      }
      
      this.setOutputValue('visibleObjects', visibleObjects);
      this.setOutputValue('occludedObjects', occludedObjects);
      this.setOutputValue('visibleCount', visibleObjects.length);
      
    } catch (error) {
      console.error('遮挡剔除失败:', error);
      throw error;
    }
  }
}

/**
 * 210 - LOD管理节点
 * 细节层次管理
 */
export class LODManagementNode extends BaseNode {
  public static readonly TYPE = 'LODManagement';
  public static readonly TITLE = 'LOD管理';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = LODManagementNode.TITLE;
    
    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机');
    this.addInputPort('object', DataType.OBJECT, '对象');
    this.addInputPort('lodLevels', DataType.ARRAY, 'LOD级别数组');
    
    // 输出端口
    this.addOutputPort('currentLOD', DataType.OBJECT, '当前LOD对象');
    this.addOutputPort('lodLevel', DataType.NUMBER, 'LOD级别');
    this.addOutputPort('distance', DataType.NUMBER, '距离');
    
    // 属性
    this.addProperty('autoUpdate', DataType.BOOLEAN, true, '自动更新');
    this.addProperty('hysteresis', DataType.NUMBER, 0.1, '滞后系数');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const camera = this.getInputValue('camera');
      const object = this.getInputValue('object');
      const lodLevels = this.getInputValue('lodLevels') || [];
      
      if (!camera || !object || lodLevels.length === 0) {
        this.setOutputValue('currentLOD', object);
        this.setOutputValue('lodLevel', 0);
        this.setOutputValue('distance', 0);
        return;
      }
      
      // 计算距离
      const distance = camera.position.distanceTo(object.position);
      const hysteresis = this.getProperty('hysteresis');
      
      // 选择合适的LOD级别
      let selectedLevel = 0;
      let selectedLOD = lodLevels[0];
      
      for (let i = 0; i < lodLevels.length; i++) {
        const level = lodLevels[i];
        let threshold = level.distance || (i * 10);
        
        // 应用滞后效应
        if (i > selectedLevel) {
          threshold *= (1 + hysteresis);
        } else if (i < selectedLevel) {
          threshold *= (1 - hysteresis);
        }
        
        if (distance >= threshold) {
          selectedLevel = i;
          selectedLOD = level;
        } else {
          break;
        }
      }
      
      this.setOutputValue('currentLOD', selectedLOD.object || selectedLOD);
      this.setOutputValue('lodLevel', selectedLevel);
      this.setOutputValue('distance', distance);
      
    } catch (error) {
      console.error('LOD管理失败:', error);
      throw error;
    }
  }
}

/**
 * 211 - 渲染统计节点
 * 渲染性能统计
 */
export class RenderStatsNode extends BaseNode {
  public static readonly TYPE = 'RenderStats';
  public static readonly TITLE = '渲染统计';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  private stats = {
    frameCount: 0,
    drawCalls: 0,
    triangles: 0,
    points: 0,
    lines: 0,
    fps: 0,
    frameTime: 0,
    lastTime: 0
  };

  constructor() {
    super();
    this.title = RenderStatsNode.TITLE;
    
    // 输入端口
    this.addInputPort('renderer', DataType.OBJECT, '渲染器');
    this.addInputPort('reset', DataType.BOOLEAN, '重置统计');
    
    // 输出端口
    this.addOutputPort('stats', DataType.OBJECT, '统计信息');
    this.addOutputPort('fps', DataType.NUMBER, '帧率');
    this.addOutputPort('drawCalls', DataType.NUMBER, '绘制调用');
    this.addOutputPort('triangles', DataType.NUMBER, '三角形数量');
    
    // 属性
    this.addProperty('updateInterval', DataType.NUMBER, 1000, '更新间隔(ms)');
    this.addProperty('trackMemory', DataType.BOOLEAN, true, '跟踪内存使用');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const renderer = this.getInputValue('renderer');
      const reset = this.getInputValue('reset');
      
      if (reset) {
        this.resetStats();
      }
      
      if (renderer && renderer.info) {
        const info = renderer.info;
        const currentTime = performance.now();
        
        // 更新统计信息
        this.stats.frameCount++;
        this.stats.drawCalls = info.render.calls;
        this.stats.triangles = info.render.triangles;
        this.stats.points = info.render.points;
        this.stats.lines = info.render.lines;
        
        // 计算帧率
        if (this.stats.lastTime > 0) {
          this.stats.frameTime = currentTime - this.stats.lastTime;
          this.stats.fps = 1000 / this.stats.frameTime;
        }
        this.stats.lastTime = currentTime;
        
        // 内存统计
        if (this.getProperty('trackMemory') && info.memory) {
          this.stats.geometries = info.memory.geometries;
          this.stats.textures = info.memory.textures;
        }
      }
      
      this.setOutputValue('stats', { ...this.stats });
      this.setOutputValue('fps', this.stats.fps);
      this.setOutputValue('drawCalls', this.stats.drawCalls);
      this.setOutputValue('triangles', this.stats.triangles);
      
    } catch (error) {
      console.error('渲染统计失败:', error);
      throw error;
    }
  }

  private resetStats(): void {
    this.stats = {
      frameCount: 0,
      drawCalls: 0,
      triangles: 0,
      points: 0,
      lines: 0,
      fps: 0,
      frameTime: 0,
      lastTime: 0
    };
  }
}
