/**
 * 第五批次：光照系统节点 (241-250)
 * 实现光照系统功能节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 241 - 环境光节点
 * 环境光照设置
 */
export class AmbientLightNode extends BaseNode {
  public static readonly TYPE = 'AmbientLight';
  public static readonly TITLE = '环境光';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = AmbientLightNode.TITLE;
    
    // 输入端口
    this.addInputPort('color', DataType.OBJECT, '光照颜色');
    this.addInputPort('intensity', DataType.NUMBER, '光照强度');
    this.addInputPort('enabled', DataType.BOOLEAN, '启用光照');
    
    // 输出端口
    this.addOutputPort('light', DataType.OBJECT, '环境光对象');
    this.addOutputPort('color', DataType.OBJECT, '当前颜色');
    this.addOutputPort('intensity', DataType.NUMBER, '当前强度');
    
    // 属性
    this.addProperty('defaultColor', DataType.STRING, '#ffffff', '默认颜色');
    this.addProperty('defaultIntensity', DataType.NUMBER, 0.5, '默认强度');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const color = this.getInputValue('color') || new THREE.Color(this.getProperty('defaultColor'));
      const intensity = this.getInputValue('intensity') || this.getProperty('defaultIntensity');
      const enabled = this.getInputValue('enabled') !== false;
      
      // 创建环境光
      const ambientLight = new THREE.AmbientLight(color, intensity);
      ambientLight.visible = enabled;
      
      this.setOutputValue('light', ambientLight);
      this.setOutputValue('color', ambientLight.color);
      this.setOutputValue('intensity', ambientLight.intensity);
      
    } catch (error) {
      console.error('环境光创建失败:', error);
      throw error;
    }
  }
}

/**
 * 242 - 方向光节点
 * 方向光源创建
 */
export class DirectionalLightNode extends BaseNode {
  public static readonly TYPE = 'DirectionalLight';
  public static readonly TITLE = '方向光';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = DirectionalLightNode.TITLE;
    
    // 输入端口
    this.addInputPort('color', DataType.OBJECT, '光照颜色');
    this.addInputPort('intensity', DataType.NUMBER, '光照强度');
    this.addInputPort('direction', DataType.OBJECT, '光照方向');
    this.addInputPort('target', DataType.OBJECT, '目标位置');
    this.addInputPort('castShadow', DataType.BOOLEAN, '投射阴影');
    
    // 输出端口
    this.addOutputPort('light', DataType.OBJECT, '方向光对象');
    this.addOutputPort('target', DataType.OBJECT, '光照目标');
    this.addOutputPort('shadow', DataType.OBJECT, '阴影对象');
    
    // 属性
    this.addProperty('defaultColor', DataType.STRING, '#ffffff', '默认颜色');
    this.addProperty('defaultIntensity', DataType.NUMBER, 1.0, '默认强度');
    this.addProperty('shadowMapSize', DataType.NUMBER, 1024, '阴影贴图大小');
    this.addProperty('shadowCameraNear', DataType.NUMBER, 0.5, '阴影相机近平面');
    this.addProperty('shadowCameraFar', DataType.NUMBER, 500, '阴影相机远平面');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const color = this.getInputValue('color') || new THREE.Color(this.getProperty('defaultColor'));
      const intensity = this.getInputValue('intensity') || this.getProperty('defaultIntensity');
      const direction = this.getInputValue('direction');
      const target = this.getInputValue('target');
      const castShadow = this.getInputValue('castShadow') || false;
      
      // 创建方向光
      const directionalLight = new THREE.DirectionalLight(color, intensity);
      
      // 设置方向
      if (direction) {
        directionalLight.position.copy(direction).normalize().multiplyScalar(10);
      } else {
        directionalLight.position.set(5, 10, 5);
      }
      
      // 设置目标
      if (target) {
        directionalLight.target.position.copy(target);
      }
      
      // 设置阴影
      directionalLight.castShadow = castShadow;
      if (castShadow) {
        const shadowMapSize = this.getProperty('shadowMapSize');
        directionalLight.shadow.mapSize.width = shadowMapSize;
        directionalLight.shadow.mapSize.height = shadowMapSize;
        directionalLight.shadow.camera.near = this.getProperty('shadowCameraNear');
        directionalLight.shadow.camera.far = this.getProperty('shadowCameraFar');
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
      }
      
      this.setOutputValue('light', directionalLight);
      this.setOutputValue('target', directionalLight.target);
      this.setOutputValue('shadow', directionalLight.shadow);
      
    } catch (error) {
      console.error('方向光创建失败:', error);
      throw error;
    }
  }
}

/**
 * 243 - 点光源节点
 * 点光源创建
 */
export class PointLightNode extends BaseNode {
  public static readonly TYPE = 'PointLight';
  public static readonly TITLE = '点光源';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = PointLightNode.TITLE;
    
    // 输入端口
    this.addInputPort('color', DataType.OBJECT, '光照颜色');
    this.addInputPort('intensity', DataType.NUMBER, '光照强度');
    this.addInputPort('distance', DataType.NUMBER, '光照距离');
    this.addInputPort('decay', DataType.NUMBER, '衰减系数');
    this.addInputPort('position', DataType.OBJECT, '光源位置');
    this.addInputPort('castShadow', DataType.BOOLEAN, '投射阴影');
    
    // 输出端口
    this.addOutputPort('light', DataType.OBJECT, '点光源对象');
    this.addOutputPort('shadow', DataType.OBJECT, '阴影对象');
    
    // 属性
    this.addProperty('defaultColor', DataType.STRING, '#ffffff', '默认颜色');
    this.addProperty('defaultIntensity', DataType.NUMBER, 1.0, '默认强度');
    this.addProperty('defaultDistance', DataType.NUMBER, 0, '默认距离');
    this.addProperty('defaultDecay', DataType.NUMBER, 1, '默认衰减');
    this.addProperty('shadowMapSize', DataType.NUMBER, 512, '阴影贴图大小');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const color = this.getInputValue('color') || new THREE.Color(this.getProperty('defaultColor'));
      const intensity = this.getInputValue('intensity') || this.getProperty('defaultIntensity');
      const distance = this.getInputValue('distance') || this.getProperty('defaultDistance');
      const decay = this.getInputValue('decay') || this.getProperty('defaultDecay');
      const position = this.getInputValue('position') || new THREE.Vector3(0, 10, 0);
      const castShadow = this.getInputValue('castShadow') || false;
      
      // 创建点光源
      const pointLight = new THREE.PointLight(color, intensity, distance, decay);
      pointLight.position.copy(position);
      
      // 设置阴影
      pointLight.castShadow = castShadow;
      if (castShadow) {
        const shadowMapSize = this.getProperty('shadowMapSize');
        pointLight.shadow.mapSize.width = shadowMapSize;
        pointLight.shadow.mapSize.height = shadowMapSize;
        pointLight.shadow.camera.near = 0.1;
        pointLight.shadow.camera.far = distance || 100;
      }
      
      this.setOutputValue('light', pointLight);
      this.setOutputValue('shadow', pointLight.shadow);
      
    } catch (error) {
      console.error('点光源创建失败:', error);
      throw error;
    }
  }
}

/**
 * 244 - 聚光灯节点
 * 聚光灯创建
 */
export class SpotLightNode extends BaseNode {
  public static readonly TYPE = 'SpotLight';
  public static readonly TITLE = '聚光灯';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = SpotLightNode.TITLE;
    
    // 输入端口
    this.addInputPort('color', DataType.OBJECT, '光照颜色');
    this.addInputPort('intensity', DataType.NUMBER, '光照强度');
    this.addInputPort('distance', DataType.NUMBER, '光照距离');
    this.addInputPort('angle', DataType.NUMBER, '光锥角度');
    this.addInputPort('penumbra', DataType.NUMBER, '半影');
    this.addInputPort('decay', DataType.NUMBER, '衰减系数');
    this.addInputPort('position', DataType.OBJECT, '光源位置');
    this.addInputPort('target', DataType.OBJECT, '目标位置');
    this.addInputPort('castShadow', DataType.BOOLEAN, '投射阴影');
    
    // 输出端口
    this.addOutputPort('light', DataType.OBJECT, '聚光灯对象');
    this.addOutputPort('target', DataType.OBJECT, '光照目标');
    this.addOutputPort('shadow', DataType.OBJECT, '阴影对象');
    
    // 属性
    this.addProperty('defaultColor', DataType.STRING, '#ffffff', '默认颜色');
    this.addProperty('defaultIntensity', DataType.NUMBER, 1.0, '默认强度');
    this.addProperty('defaultAngle', DataType.NUMBER, Math.PI / 3, '默认角度');
    this.addProperty('defaultPenumbra', DataType.NUMBER, 0, '默认半影');
    this.addProperty('shadowMapSize', DataType.NUMBER, 1024, '阴影贴图大小');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const color = this.getInputValue('color') || new THREE.Color(this.getProperty('defaultColor'));
      const intensity = this.getInputValue('intensity') || this.getProperty('defaultIntensity');
      const distance = this.getInputValue('distance') || 0;
      const angle = this.getInputValue('angle') || this.getProperty('defaultAngle');
      const penumbra = this.getInputValue('penumbra') || this.getProperty('defaultPenumbra');
      const decay = this.getInputValue('decay') || 1;
      const position = this.getInputValue('position') || new THREE.Vector3(0, 10, 0);
      const target = this.getInputValue('target') || new THREE.Vector3(0, 0, 0);
      const castShadow = this.getInputValue('castShadow') || false;
      
      // 创建聚光灯
      const spotLight = new THREE.SpotLight(color, intensity, distance, angle, penumbra, decay);
      spotLight.position.copy(position);
      spotLight.target.position.copy(target);
      
      // 设置阴影
      spotLight.castShadow = castShadow;
      if (castShadow) {
        const shadowMapSize = this.getProperty('shadowMapSize');
        spotLight.shadow.mapSize.width = shadowMapSize;
        spotLight.shadow.mapSize.height = shadowMapSize;
        spotLight.shadow.camera.near = 0.5;
        spotLight.shadow.camera.far = distance || 100;
        spotLight.shadow.camera.fov = (angle * 180) / Math.PI;
      }
      
      this.setOutputValue('light', spotLight);
      this.setOutputValue('target', spotLight.target);
      this.setOutputValue('shadow', spotLight.shadow);
      
    } catch (error) {
      console.error('聚光灯创建失败:', error);
      throw error;
    }
  }
}

/**
 * 245 - 区域光节点
 * 区域光源创建
 */
export class AreaLightNode extends BaseNode {
  public static readonly TYPE = 'AreaLight';
  public static readonly TITLE = '区域光';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = AreaLightNode.TITLE;
    
    // 输入端口
    this.addInputPort('color', DataType.OBJECT, '光照颜色');
    this.addInputPort('intensity', DataType.NUMBER, '光照强度');
    this.addInputPort('width', DataType.NUMBER, '光源宽度');
    this.addInputPort('height', DataType.NUMBER, '光源高度');
    this.addInputPort('position', DataType.OBJECT, '光源位置');
    this.addInputPort('rotation', DataType.OBJECT, '光源旋转');
    
    // 输出端口
    this.addOutputPort('light', DataType.OBJECT, '区域光对象');
    this.addOutputPort('helper', DataType.OBJECT, '辅助对象');
    
    // 属性
    this.addProperty('defaultColor', DataType.STRING, '#ffffff', '默认颜色');
    this.addProperty('defaultIntensity', DataType.NUMBER, 1.0, '默认强度');
    this.addProperty('defaultWidth', DataType.NUMBER, 10, '默认宽度');
    this.addProperty('defaultHeight', DataType.NUMBER, 10, '默认高度');
    this.addProperty('showHelper', DataType.BOOLEAN, false, '显示辅助线');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const color = this.getInputValue('color') || new THREE.Color(this.getProperty('defaultColor'));
      const intensity = this.getInputValue('intensity') || this.getProperty('defaultIntensity');
      const width = this.getInputValue('width') || this.getProperty('defaultWidth');
      const height = this.getInputValue('height') || this.getProperty('defaultHeight');
      const position = this.getInputValue('position') || new THREE.Vector3(0, 10, 0);
      const rotation = this.getInputValue('rotation');
      
      // 创建矩形区域光
      const rectAreaLight = new THREE.RectAreaLight(color, intensity, width, height);
      rectAreaLight.position.copy(position);
      
      if (rotation) {
        rectAreaLight.rotation.copy(rotation);
      }
      
      // 创建辅助对象
      let helper = null;
      if (this.getProperty('showHelper')) {
        helper = new THREE.RectAreaLightHelper(rectAreaLight);
      }
      
      this.setOutputValue('light', rectAreaLight);
      this.setOutputValue('helper', helper);
      
    } catch (error) {
      console.error('区域光创建失败:', error);
      throw error;
    }
  }
}

/**
 * 246 - 光照强度节点
 * 光照强度调节
 */
export class LightIntensityNode extends BaseNode {
  public static readonly TYPE = 'LightIntensity';
  public static readonly TITLE = '光照强度';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = LightIntensityNode.TITLE;

    // 输入端口
    this.addInputPort('light', DataType.OBJECT, '光源对象');
    this.addInputPort('intensity', DataType.NUMBER, '强度值');
    this.addInputPort('animate', DataType.BOOLEAN, '动画过渡');

    // 输出端口
    this.addOutputPort('light', DataType.OBJECT, '光源对象');
    this.addOutputValue('currentIntensity', DataType.NUMBER, '当前强度');

    // 属性
    this.addProperty('minIntensity', DataType.NUMBER, 0, '最小强度');
    this.addProperty('maxIntensity', DataType.NUMBER, 10, '最大强度');
    this.addProperty('animationSpeed', DataType.NUMBER, 0.1, '动画速度');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const light = this.getInputValue('light');
      const intensity = this.getInputValue('intensity');
      const animate = this.getInputValue('animate') || false;

      if (!light) {
        throw new Error('需要光源对象');
      }

      if (intensity !== undefined) {
        // 限制强度范围
        const clampedIntensity = Math.max(
          this.getProperty('minIntensity'),
          Math.min(this.getProperty('maxIntensity'), intensity)
        );

        if (animate) {
          // 动画过渡
          const speed = this.getProperty('animationSpeed');
          light.intensity += (clampedIntensity - light.intensity) * speed;
        } else {
          // 直接设置
          light.intensity = clampedIntensity;
        }
      }

      this.setOutputValue('light', light);
      this.setOutputValue('currentIntensity', light.intensity);

    } catch (error) {
      console.error('光照强度调节失败:', error);
      throw error;
    }
  }
}

/**
 * 247 - 光照颜色节点
 * 光照颜色设置
 */
export class LightColorNode extends BaseNode {
  public static readonly TYPE = 'LightColor';
  public static readonly TITLE = '光照颜色';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = LightColorNode.TITLE;

    // 输入端口
    this.addInputPort('light', DataType.OBJECT, '光源对象');
    this.addInputPort('color', DataType.OBJECT, '颜色值');
    this.addInputPort('hex', DataType.STRING, '十六进制颜色');
    this.addInputPort('r', DataType.NUMBER, '红色分量');
    this.addInputPort('g', DataType.NUMBER, '绿色分量');
    this.addInputPort('b', DataType.NUMBER, '蓝色分量');
    this.addInputPort('animate', DataType.BOOLEAN, '动画过渡');

    // 输出端口
    this.addOutputPort('light', DataType.OBJECT, '光源对象');
    this.addOutputPort('currentColor', DataType.OBJECT, '当前颜色');

    // 属性
    this.addProperty('animationSpeed', DataType.NUMBER, 0.1, '动画速度');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const light = this.getInputValue('light');
      const color = this.getInputValue('color');
      const hex = this.getInputValue('hex');
      const r = this.getInputValue('r');
      const g = this.getInputValue('g');
      const b = this.getInputValue('b');
      const animate = this.getInputValue('animate') || false;

      if (!light) {
        throw new Error('需要光源对象');
      }

      let targetColor;

      if (color) {
        targetColor = color;
      } else if (hex) {
        targetColor = new THREE.Color(hex);
      } else if (r !== undefined && g !== undefined && b !== undefined) {
        targetColor = new THREE.Color(r, g, b);
      }

      if (targetColor) {
        if (animate) {
          // 动画过渡
          const speed = this.getProperty('animationSpeed');
          light.color.lerp(targetColor, speed);
        } else {
          // 直接设置
          light.color.copy(targetColor);
        }
      }

      this.setOutputValue('light', light);
      this.setOutputValue('currentColor', light.color.clone());

    } catch (error) {
      console.error('光照颜色设置失败:', error);
      throw error;
    }
  }
}

/**
 * 248 - 光照阴影节点
 * 阴影投射设置
 */
export class LightShadowNode extends BaseNode {
  public static readonly TYPE = 'LightShadow';
  public static readonly TITLE = '光照阴影';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = LightShadowNode.TITLE;

    // 输入端口
    this.addInputPort('light', DataType.OBJECT, '光源对象');
    this.addInputPort('castShadow', DataType.BOOLEAN, '投射阴影');
    this.addInputPort('mapSize', DataType.NUMBER, '阴影贴图大小');
    this.addInputPort('bias', DataType.NUMBER, '阴影偏移');
    this.addInputPort('normalBias', DataType.NUMBER, '法线偏移');
    this.addInputPort('radius', DataType.NUMBER, '阴影半径');

    // 输出端口
    this.addOutputPort('light', DataType.OBJECT, '光源对象');
    this.addOutputPort('shadow', DataType.OBJECT, '阴影对象');

    // 属性
    this.addProperty('defaultMapSize', DataType.NUMBER, 1024, '默认贴图大小');
    this.addProperty('defaultBias', DataType.NUMBER, 0, '默认偏移');
    this.addProperty('defaultNormalBias', DataType.NUMBER, 0, '默认法线偏移');
    this.addProperty('defaultRadius', DataType.NUMBER, 1, '默认半径');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const light = this.getInputValue('light');
      const castShadow = this.getInputValue('castShadow');
      const mapSize = this.getInputValue('mapSize') || this.getProperty('defaultMapSize');
      const bias = this.getInputValue('bias') || this.getProperty('defaultBias');
      const normalBias = this.getInputValue('normalBias') || this.getProperty('defaultNormalBias');
      const radius = this.getInputValue('radius') || this.getProperty('defaultRadius');

      if (!light) {
        throw new Error('需要光源对象');
      }

      // 设置阴影投射
      if (castShadow !== undefined) {
        light.castShadow = castShadow;
      }

      // 配置阴影属性
      if (light.shadow) {
        light.shadow.mapSize.width = mapSize;
        light.shadow.mapSize.height = mapSize;
        light.shadow.bias = bias;
        light.shadow.normalBias = normalBias;
        light.shadow.radius = radius;

        // 更新阴影相机
        if (light.shadow.camera) {
          light.shadow.camera.updateProjectionMatrix();
        }
      }

      this.setOutputValue('light', light);
      this.setOutputValue('shadow', light.shadow);

    } catch (error) {
      console.error('光照阴影设置失败:', error);
      throw error;
    }
  }
}

/**
 * 249 - 阴影质量节点
 * 阴影质量控制
 */
export class ShadowQualityNode extends BaseNode {
  public static readonly TYPE = 'ShadowQuality';
  public static readonly TITLE = '阴影质量';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = ShadowQualityNode.TITLE;

    // 输入端口
    this.addInputPort('renderer', DataType.OBJECT, '渲染器');
    this.addInputPort('quality', DataType.STRING, '质量等级');
    this.addInputPort('enabled', DataType.BOOLEAN, '启用阴影');

    // 输出端口
    this.addOutputPort('renderer', DataType.OBJECT, '渲染器');
    this.addOutputPort('shadowType', DataType.STRING, '阴影类型');

    // 属性
    this.addProperty('autoUpdate', DataType.BOOLEAN, true, '自动更新');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const renderer = this.getInputValue('renderer');
      const quality = this.getInputValue('quality') || 'medium';
      const enabled = this.getInputValue('enabled') !== false;

      if (!renderer) {
        throw new Error('需要渲染器对象');
      }

      // 启用/禁用阴影
      renderer.shadowMap.enabled = enabled;
      renderer.shadowMap.autoUpdate = this.getProperty('autoUpdate');

      // 根据质量等级设置阴影类型
      let shadowType;
      switch (quality.toLowerCase()) {
        case 'low':
          shadowType = THREE.BasicShadowMap;
          break;
        case 'medium':
          shadowType = THREE.PCFShadowMap;
          break;
        case 'high':
          shadowType = THREE.PCFSoftShadowMap;
          break;
        case 'ultra':
          shadowType = THREE.VSMShadowMap;
          break;
        default:
          shadowType = THREE.PCFShadowMap;
      }

      renderer.shadowMap.type = shadowType;

      this.setOutputValue('renderer', renderer);
      this.setOutputValue('shadowType', shadowType);

    } catch (error) {
      console.error('阴影质量设置失败:', error);
      throw error;
    }
  }
}

/**
 * 250 - 阴影距离节点
 * 阴影渲染距离
 */
export class ShadowDistanceNode extends BaseNode {
  public static readonly TYPE = 'ShadowDistance';
  public static readonly TITLE = '阴影距离';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = ShadowDistanceNode.TITLE;

    // 输入端口
    this.addInputPort('light', DataType.OBJECT, '光源对象');
    this.addInputPort('near', DataType.NUMBER, '近距离');
    this.addInputPort('far', DataType.NUMBER, '远距离');
    this.addInputPort('left', DataType.NUMBER, '左边界');
    this.addInputPort('right', DataType.NUMBER, '右边界');
    this.addInputPort('top', DataType.NUMBER, '上边界');
    this.addInputPort('bottom', DataType.NUMBER, '下边界');

    // 输出端口
    this.addOutputPort('light', DataType.OBJECT, '光源对象');
    this.addOutputPort('shadowCamera', DataType.OBJECT, '阴影相机');

    // 属性
    this.addProperty('autoFit', DataType.BOOLEAN, true, '自动适配');
    this.addProperty('margin', DataType.NUMBER, 10, '边距');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const light = this.getInputValue('light');
      const near = this.getInputValue('near');
      const far = this.getInputValue('far');
      const left = this.getInputValue('left');
      const right = this.getInputValue('right');
      const top = this.getInputValue('top');
      const bottom = this.getInputValue('bottom');

      if (!light || !light.shadow || !light.shadow.camera) {
        throw new Error('需要带有阴影的光源对象');
      }

      const shadowCamera = light.shadow.camera;

      // 设置阴影相机参数
      if (near !== undefined) shadowCamera.near = near;
      if (far !== undefined) shadowCamera.far = far;

      // 对于正交相机（方向光）
      if (shadowCamera.isOrthographicCamera) {
        const margin = this.getProperty('margin');

        if (left !== undefined) shadowCamera.left = left - margin;
        if (right !== undefined) shadowCamera.right = right + margin;
        if (top !== undefined) shadowCamera.top = top + margin;
        if (bottom !== undefined) shadowCamera.bottom = bottom - margin;

        // 自动适配场景边界
        if (this.getProperty('autoFit')) {
          // 这里可以添加场景边界计算逻辑
          shadowCamera.left = shadowCamera.left || -50;
          shadowCamera.right = shadowCamera.right || 50;
          shadowCamera.top = shadowCamera.top || 50;
          shadowCamera.bottom = shadowCamera.bottom || -50;
        }
      }

      // 对于透视相机（聚光灯、点光源）
      if (shadowCamera.isPerspectiveCamera) {
        if (light.angle) {
          shadowCamera.fov = (light.angle * 180) / Math.PI;
        }
      }

      // 更新投影矩阵
      shadowCamera.updateProjectionMatrix();

      this.setOutputValue('light', light);
      this.setOutputValue('shadowCamera', shadowCamera);

    } catch (error) {
      console.error('阴影距离设置失败:', error);
      throw error;
    }
  }
}
