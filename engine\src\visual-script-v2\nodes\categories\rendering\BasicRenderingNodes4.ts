/**
 * 第五批次：基础渲染系统节点 (219-220) - 第四部分
 * 完成最后的基础渲染功能节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 219 - 点渲染节点
 * 点渲染模式
 */
export class PointRenderNode extends BaseNode {
  public static readonly TYPE = 'PointRender';
  public static readonly TITLE = '点渲染';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = PointRenderNode.TITLE;
    
    // 输入端口
    this.addInputPort('geometry', DataType.OBJECT, '几何体');
    this.addInputPort('material', DataType.OBJECT, '点材质');
    this.addInputPort('positions', DataType.ARRAY, '点位置数组');
    this.addInputPort('colors', DataType.ARRAY, '点颜色数组');
    this.addInputPort('sizes', DataType.ARRAY, '点大小数组');
    
    // 输出端口
    this.addOutputPort('points', DataType.OBJECT, '点对象');
    this.addOutputPort('pointCount', DataType.NUMBER, '点数量');
    
    // 属性
    this.addProperty('size', DataType.NUMBER, 1.0, '默认点大小');
    this.addProperty('sizeAttenuation', DataType.BOOLEAN, true, '大小衰减');
    this.addProperty('alphaTest', DataType.NUMBER, 0.5, 'Alpha测试值');
    this.addProperty('vertexColors', DataType.BOOLEAN, false, '顶点颜色');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      let geometry = this.getInputValue('geometry');
      const material = this.getInputValue('material');
      const positions = this.getInputValue('positions');
      const colors = this.getInputValue('colors');
      const sizes = this.getInputValue('sizes');
      
      // 如果没有几何体但有位置数据，创建点几何体
      if (!geometry && positions) {
        geometry = new THREE.BufferGeometry();
        
        // 设置位置属性
        const positionArray = new Float32Array(positions.length * 3);
        for (let i = 0; i < positions.length; i++) {
          const pos = positions[i];
          positionArray[i * 3] = pos.x || pos[0] || 0;
          positionArray[i * 3 + 1] = pos.y || pos[1] || 0;
          positionArray[i * 3 + 2] = pos.z || pos[2] || 0;
        }
        geometry.setAttribute('position', new THREE.BufferAttribute(positionArray, 3));
        
        // 设置颜色属性
        if (colors && this.getProperty('vertexColors')) {
          const colorArray = new Float32Array(colors.length * 3);
          for (let i = 0; i < colors.length; i++) {
            const color = colors[i];
            if (color instanceof THREE.Color) {
              colorArray[i * 3] = color.r;
              colorArray[i * 3 + 1] = color.g;
              colorArray[i * 3 + 2] = color.b;
            } else {
              colorArray[i * 3] = color.r || color[0] || 1;
              colorArray[i * 3 + 1] = color.g || color[1] || 1;
              colorArray[i * 3 + 2] = color.b || color[2] || 1;
            }
          }
          geometry.setAttribute('color', new THREE.BufferAttribute(colorArray, 3));
        }
        
        // 设置大小属性
        if (sizes) {
          const sizeArray = new Float32Array(sizes);
          geometry.setAttribute('size', new THREE.BufferAttribute(sizeArray, 1));
        }
      }
      
      if (!geometry) {
        throw new Error('需要几何体或位置数据');
      }
      
      // 创建或使用点材质
      let pointMaterial = material;
      if (!pointMaterial) {
        pointMaterial = new THREE.PointsMaterial({
          size: this.getProperty('size'),
          sizeAttenuation: this.getProperty('sizeAttenuation'),
          alphaTest: this.getProperty('alphaTest'),
          vertexColors: this.getProperty('vertexColors')
        });
      } else if (pointMaterial.isPointsMaterial) {
        // 更新现有点材质属性
        pointMaterial.size = this.getProperty('size');
        pointMaterial.sizeAttenuation = this.getProperty('sizeAttenuation');
        pointMaterial.alphaTest = this.getProperty('alphaTest');
        pointMaterial.vertexColors = this.getProperty('vertexColors');
      }
      
      // 创建点对象
      const points = new THREE.Points(geometry, pointMaterial);
      
      // 计算点数量
      const pointCount = geometry.attributes.position ? 
        geometry.attributes.position.count : 0;
      
      this.setOutputValue('points', points);
      this.setOutputValue('pointCount', pointCount);
      
    } catch (error) {
      console.error('点渲染失败:', error);
      throw error;
    }
  }
}

/**
 * 220 - 渲染状态节点
 * 渲染状态管理
 */
export class RenderStateNode extends BaseNode {
  public static readonly TYPE = 'RenderState';
  public static readonly TITLE = '渲染状态';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  private renderState = {
    enabled: true,
    autoClear: true,
    autoClearColor: true,
    autoClearDepth: true,
    autoClearStencil: true,
    sortObjects: true,
    clippingPlanes: [],
    localClippingEnabled: false,
    shadowMap: {
      enabled: false,
      type: THREE.PCFSoftShadowMap,
      autoUpdate: true
    },
    toneMapping: THREE.NoToneMapping,
    toneMappingExposure: 1.0,
    physicallyCorrectLights: false,
    outputEncoding: THREE.sRGBEncoding,
    gammaFactor: 2.2
  };

  constructor() {
    super();
    this.title = RenderStateNode.TITLE;
    
    // 输入端口
    this.addInputPort('renderer', DataType.OBJECT, '渲染器');
    this.addInputPort('enabled', DataType.BOOLEAN, '启用渲染');
    this.addInputPort('reset', DataType.BOOLEAN, '重置状态');
    
    // 输出端口
    this.addOutputPort('state', DataType.OBJECT, '渲染状态');
    this.addOutputPort('renderer', DataType.OBJECT, '配置后的渲染器');
    
    // 属性
    this.addProperty('autoClear', DataType.BOOLEAN, true, '自动清除');
    this.addProperty('sortObjects', DataType.BOOLEAN, true, '对象排序');
    this.addProperty('shadowMapEnabled', DataType.BOOLEAN, false, '阴影映射');
    this.addProperty('shadowMapType', DataType.STRING, 'PCFSoftShadowMap', '阴影类型');
    this.addProperty('toneMapping', DataType.STRING, 'NoToneMapping', '色调映射');
    this.addProperty('toneMappingExposure', DataType.NUMBER, 1.0, '曝光度');
    this.addProperty('physicallyCorrectLights', DataType.BOOLEAN, false, '物理正确光照');
    this.addProperty('outputEncoding', DataType.STRING, 'sRGBEncoding', '输出编码');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const renderer = this.getInputValue('renderer');
      const enabled = this.getInputValue('enabled') !== false;
      const reset = this.getInputValue('reset');
      
      if (reset) {
        this.resetState();
      }
      
      // 更新渲染状态
      this.renderState.enabled = enabled;
      this.renderState.autoClear = this.getProperty('autoClear');
      this.renderState.sortObjects = this.getProperty('sortObjects');
      this.renderState.shadowMap.enabled = this.getProperty('shadowMapEnabled');
      this.renderState.shadowMap.type = THREE[this.getProperty('shadowMapType')] || THREE.PCFSoftShadowMap;
      this.renderState.toneMapping = THREE[this.getProperty('toneMapping')] || THREE.NoToneMapping;
      this.renderState.toneMappingExposure = this.getProperty('toneMappingExposure');
      this.renderState.physicallyCorrectLights = this.getProperty('physicallyCorrectLights');
      this.renderState.outputEncoding = THREE[this.getProperty('outputEncoding')] || THREE.sRGBEncoding;
      
      // 应用到渲染器
      if (renderer) {
        renderer.autoClear = this.renderState.autoClear;
        renderer.autoClearColor = this.renderState.autoClearColor;
        renderer.autoClearDepth = this.renderState.autoClearDepth;
        renderer.autoClearStencil = this.renderState.autoClearStencil;
        renderer.sortObjects = this.renderState.sortObjects;
        
        // 阴影设置
        renderer.shadowMap.enabled = this.renderState.shadowMap.enabled;
        renderer.shadowMap.type = this.renderState.shadowMap.type;
        renderer.shadowMap.autoUpdate = this.renderState.shadowMap.autoUpdate;
        
        // 色调映射和编码
        renderer.toneMapping = this.renderState.toneMapping;
        renderer.toneMappingExposure = this.renderState.toneMappingExposure;
        renderer.physicallyCorrectLights = this.renderState.physicallyCorrectLights;
        renderer.outputEncoding = this.renderState.outputEncoding;
        
        // 裁剪平面
        if (this.renderState.clippingPlanes.length > 0) {
          renderer.clippingPlanes = this.renderState.clippingPlanes;
          renderer.localClippingEnabled = this.renderState.localClippingEnabled;
        }
      }
      
      this.setOutputValue('state', { ...this.renderState });
      this.setOutputValue('renderer', renderer);
      
    } catch (error) {
      console.error('渲染状态管理失败:', error);
      throw error;
    }
  }

  private resetState(): void {
    this.renderState = {
      enabled: true,
      autoClear: true,
      autoClearColor: true,
      autoClearDepth: true,
      autoClearStencil: true,
      sortObjects: true,
      clippingPlanes: [],
      localClippingEnabled: false,
      shadowMap: {
        enabled: false,
        type: THREE.PCFSoftShadowMap,
        autoUpdate: true
      },
      toneMapping: THREE.NoToneMapping,
      toneMappingExposure: 1.0,
      physicallyCorrectLights: false,
      outputEncoding: THREE.sRGBEncoding,
      gammaFactor: 2.2
    };
  }

  /**
   * 添加裁剪平面
   */
  public addClippingPlane(plane: THREE.Plane): void {
    this.renderState.clippingPlanes.push(plane);
    this.renderState.localClippingEnabled = true;
  }

  /**
   * 移除裁剪平面
   */
  public removeClippingPlane(plane: THREE.Plane): void {
    const index = this.renderState.clippingPlanes.indexOf(plane);
    if (index > -1) {
      this.renderState.clippingPlanes.splice(index, 1);
    }
    
    if (this.renderState.clippingPlanes.length === 0) {
      this.renderState.localClippingEnabled = false;
    }
  }

  /**
   * 清除所有裁剪平面
   */
  public clearClippingPlanes(): void {
    this.renderState.clippingPlanes = [];
    this.renderState.localClippingEnabled = false;
  }
}
