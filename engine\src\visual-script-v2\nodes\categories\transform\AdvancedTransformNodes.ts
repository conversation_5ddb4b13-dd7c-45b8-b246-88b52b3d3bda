/**
 * 高级变换节点 (151-160)
 * 实现复杂的变换操作和动画效果
 */

import { BaseNode } from '../../base/BaseNode';
import { NodeCategory, DataType, IExecutionContext } from '../../../core/types';

/**
 * 151 - 插值变换节点
 */
export class InterpolateTransformNode extends BaseNode {
  public readonly type = 'transform/interpolate';
  public readonly category = NodeCategory.TRANSFORM;

  protected getDefaultName(): string {
    return '插值变换';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'fromTransform',
      label: '起始变换',
      type: DataType.MATRIX4,
      required: true,
      description: '起始变换矩阵'
    });

    this.addInputPort({
      name: 'toTransform',
      label: '目标变换',
      type: DataType.MATRIX4,
      required: true,
      description: '目标变换矩阵'
    });

    this.addInputPort({
      name: 'factor',
      label: '插值因子',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0.5,
      description: '插值因子 (0-1)'
    });

    this.addInputPort({
      name: 'interpolationType',
      label: '插值类型',
      type: DataType.STRING,
      required: false,
      defaultValue: 'linear',
      description: '插值类型: linear, ease, easeIn, easeOut'
    });

    this.addOutputPort({
      name: 'result',
      label: '结果变换',
      type: DataType.MATRIX4,
      required: false,
      description: '插值后的变换矩阵'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const fromTransform = this.getInput(context, 'fromTransform');
    const toTransform = this.getInput(context, 'toTransform');
    const factor = Math.max(0, Math.min(1, this.getInput(context, 'factor', 0.5)));
    const interpolationType = this.getInput(context, 'interpolationType', 'linear');

    if (!fromTransform || !toTransform) {
      throw new Error('插值变换节点需要起始和目标变换矩阵');
    }

    // 应用插值类型的缓动函数
    let easedFactor = factor;
    switch (interpolationType) {
      case 'ease':
        easedFactor = 0.5 * (1 - Math.cos(Math.PI * factor));
        break;
      case 'easeIn':
        easedFactor = factor * factor;
        break;
      case 'easeOut':
        easedFactor = 1 - (1 - factor) * (1 - factor);
        break;
      default:
        easedFactor = factor;
    }

    // 简化的矩阵插值（实际应该使用四元数插值）
    const result = this.interpolateMatrix4(fromTransform, toTransform, easedFactor);
    
    this.setOutput(context, 'result', result);
  }

  private interpolateMatrix4(from: any, to: any, factor: number): any {
    // 简化实现，实际应该分解为位置、旋转、缩放分别插值
    const result = [];
    for (let i = 0; i < 16; i++) {
      result[i] = from[i] + (to[i] - from[i]) * factor;
    }
    return result;
  }
}

/**
 * 152 - 变换约束节点
 */
export class TransformConstraintNode extends BaseNode {
  public readonly type = 'transform/constraint';
  public readonly category = NodeCategory.TRANSFORM;

  protected getDefaultName(): string {
    return '变换约束';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'transform',
      label: '输入变换',
      type: DataType.MATRIX4,
      required: true,
      description: '要约束的变换矩阵'
    });

    this.addInputPort({
      name: 'constraintType',
      label: '约束类型',
      type: DataType.STRING,
      required: false,
      defaultValue: 'position',
      description: '约束类型: position, rotation, scale, all'
    });

    this.addInputPort({
      name: 'minValues',
      label: '最小值',
      type: DataType.VECTOR3,
      required: false,
      defaultValue: [-10, -10, -10],
      description: '约束的最小值'
    });

    this.addInputPort({
      name: 'maxValues',
      label: '最大值',
      type: DataType.VECTOR3,
      required: false,
      defaultValue: [10, 10, 10],
      description: '约束的最大值'
    });

    this.addOutputPort({
      name: 'constrainedTransform',
      label: '约束后变换',
      type: DataType.MATRIX4,
      required: false,
      description: '应用约束后的变换矩阵'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const transform = this.getInput(context, 'transform');
    const constraintType = this.getInput(context, 'constraintType', 'position');
    const minValues = this.getInput(context, 'minValues', [-10, -10, -10]);
    const maxValues = this.getInput(context, 'maxValues', [10, 10, 10]);

    if (!transform) {
      throw new Error('变换约束节点需要输入变换矩阵');
    }

    const constrainedTransform = this.applyConstraints(transform, constraintType, minValues, maxValues);
    
    this.setOutput(context, 'constrainedTransform', constrainedTransform);
  }

  private applyConstraints(transform: any, type: string, min: number[], max: number[]): any {
    // 简化实现，实际应该分解矩阵并约束相应分量
    const result = [...transform];
    
    if (type === 'position' || type === 'all') {
      // 约束位置分量 (矩阵的第12、13、14元素)
      result[12] = Math.max(min[0], Math.min(max[0], result[12]));
      result[13] = Math.max(min[1], Math.min(max[1], result[13]));
      result[14] = Math.max(min[2], Math.min(max[2], result[14]));
    }
    
    return result;
  }
}

/**
 * 154 - 距离计算节点
 */
export class DistanceCalculateNode extends BaseNode {
  public readonly type = 'transform/distance';
  public readonly category = NodeCategory.TRANSFORM;

  protected getDefaultName(): string {
    return '距离计算';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'position1',
      label: '位置1',
      type: DataType.VECTOR3,
      required: true,
      description: '第一个位置'
    });

    this.addInputPort({
      name: 'position2',
      label: '位置2',
      type: DataType.VECTOR3,
      required: true,
      description: '第二个位置'
    });

    this.addInputPort({
      name: 'distanceType',
      label: '距离类型',
      type: DataType.STRING,
      required: false,
      defaultValue: 'euclidean',
      description: '距离类型: euclidean, manhattan, chebyshev'
    });

    this.addOutputPort({
      name: 'distance',
      label: '距离',
      type: DataType.NUMBER,
      required: false,
      description: '计算得到的距离'
    });

    this.addOutputPort({
      name: 'direction',
      label: '方向向量',
      type: DataType.VECTOR3,
      required: false,
      description: '从位置1到位置2的方向向量'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const pos1 = this.getInput(context, 'position1');
    const pos2 = this.getInput(context, 'position2');
    const distanceType = this.getInput(context, 'distanceType', 'euclidean');

    if (!pos1 || !pos2) {
      throw new Error('距离计算节点需要两个位置向量');
    }

    const dx = pos2[0] - pos1[0];
    const dy = pos2[1] - pos1[1];
    const dz = pos2[2] - pos1[2];

    let distance: number;
    switch (distanceType) {
      case 'manhattan':
        distance = Math.abs(dx) + Math.abs(dy) + Math.abs(dz);
        break;
      case 'chebyshev':
        distance = Math.max(Math.abs(dx), Math.abs(dy), Math.abs(dz));
        break;
      default: // euclidean
        distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    // 计算方向向量（归一化）
    const length = Math.sqrt(dx * dx + dy * dy + dz * dz);
    const direction = length > 0 ? [dx / length, dy / length, dz / length] : [0, 0, 0];

    this.setOutput(context, 'distance', distance);
    this.setOutput(context, 'direction', direction);
  }
}

/**
 * 155 - 方向计算节点
 */
export class DirectionCalculateNode extends BaseNode {
  public readonly type = 'transform/direction';
  public readonly category = NodeCategory.TRANSFORM;

  protected getDefaultName(): string {
    return '方向计算';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'fromPosition',
      label: '起始位置',
      type: DataType.VECTOR3,
      required: true,
      description: '起始位置'
    });

    this.addInputPort({
      name: 'toPosition',
      label: '目标位置',
      type: DataType.VECTOR3,
      required: true,
      description: '目标位置'
    });

    this.addInputPort({
      name: 'normalize',
      label: '归一化',
      type: DataType.BOOLEAN,
      required: false,
      defaultValue: true,
      description: '是否归一化方向向量'
    });

    this.addOutputPort({
      name: 'direction',
      label: '方向向量',
      type: DataType.VECTOR3,
      required: false,
      description: '计算得到的方向向量'
    });

    this.addOutputPort({
      name: 'magnitude',
      label: '长度',
      type: DataType.NUMBER,
      required: false,
      description: '方向向量的长度'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const fromPos = this.getInput(context, 'fromPosition');
    const toPos = this.getInput(context, 'toPosition');
    const normalize = this.getInput(context, 'normalize', true);

    if (!fromPos || !toPos) {
      throw new Error('方向计算节点需要起始和目标位置');
    }

    const dx = toPos[0] - fromPos[0];
    const dy = toPos[1] - fromPos[1];
    const dz = toPos[2] - fromPos[2];

    const magnitude = Math.sqrt(dx * dx + dy * dy + dz * dz);

    let direction: number[];
    if (normalize && magnitude > 0) {
      direction = [dx / magnitude, dy / magnitude, dz / magnitude];
    } else {
      direction = [dx, dy, dz];
    }

    this.setOutput(context, 'direction', direction);
    this.setOutput(context, 'magnitude', magnitude);
  }
}

/**
 * 156 - 角度计算节点
 */
export class AngleCalculateNode extends BaseNode {
  public readonly type = 'transform/angle';
  public readonly category = NodeCategory.TRANSFORM;

  protected getDefaultName(): string {
    return '角度计算';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'vector1',
      label: '向量1',
      type: DataType.VECTOR3,
      required: true,
      description: '第一个向量'
    });

    this.addInputPort({
      name: 'vector2',
      label: '向量2',
      type: DataType.VECTOR3,
      required: true,
      description: '第二个向量'
    });

    this.addInputPort({
      name: 'angleUnit',
      label: '角度单位',
      type: DataType.STRING,
      required: false,
      defaultValue: 'radians',
      description: '角度单位: radians, degrees'
    });

    this.addOutputPort({
      name: 'angle',
      label: '夹角',
      type: DataType.NUMBER,
      required: false,
      description: '两向量间的夹角'
    });

    this.addOutputPort({
      name: 'dotProduct',
      label: '点积',
      type: DataType.NUMBER,
      required: false,
      description: '两向量的点积'
    });

    this.addOutputPort({
      name: 'crossProduct',
      label: '叉积',
      type: DataType.VECTOR3,
      required: false,
      description: '两向量的叉积'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const vec1 = this.getInput(context, 'vector1');
    const vec2 = this.getInput(context, 'vector2');
    const angleUnit = this.getInput(context, 'angleUnit', 'radians');

    if (!vec1 || !vec2) {
      throw new Error('角度计算节点需要两个向量');
    }

    // 计算点积
    const dotProduct = vec1[0] * vec2[0] + vec1[1] * vec2[1] + vec1[2] * vec2[2];

    // 计算向量长度
    const mag1 = Math.sqrt(vec1[0] * vec1[0] + vec1[1] * vec1[1] + vec1[2] * vec1[2]);
    const mag2 = Math.sqrt(vec2[0] * vec2[0] + vec2[1] * vec2[1] + vec2[2] * vec2[2]);

    // 计算夹角
    let angle = 0;
    if (mag1 > 0 && mag2 > 0) {
      const cosAngle = Math.max(-1, Math.min(1, dotProduct / (mag1 * mag2)));
      angle = Math.acos(cosAngle);

      if (angleUnit === 'degrees') {
        angle = angle * 180 / Math.PI;
      }
    }

    // 计算叉积
    const crossProduct = [
      vec1[1] * vec2[2] - vec1[2] * vec2[1],
      vec1[2] * vec2[0] - vec1[0] * vec2[2],
      vec1[0] * vec2[1] - vec1[1] * vec2[0]
    ];

    this.setOutput(context, 'angle', angle);
    this.setOutput(context, 'dotProduct', dotProduct);
    this.setOutput(context, 'crossProduct', crossProduct);
  }
}

/**
 * 157 - 变换动画节点
 */
export class TransformAnimationNode extends BaseNode {
  public readonly type = 'transform/animation';
  public readonly category = NodeCategory.TRANSFORM;

  protected getDefaultName(): string {
    return '变换动画';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'startTransform',
      label: '起始变换',
      type: DataType.MATRIX4,
      required: true,
      description: '动画起始变换'
    });

    this.addInputPort({
      name: 'endTransform',
      label: '结束变换',
      type: DataType.MATRIX4,
      required: true,
      description: '动画结束变换'
    });

    this.addInputPort({
      name: 'duration',
      label: '持续时间',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 1.0,
      description: '动画持续时间（秒）'
    });

    this.addInputPort({
      name: 'currentTime',
      label: '当前时间',
      type: DataType.NUMBER,
      required: false,
      defaultValue: 0.0,
      description: '当前动画时间'
    });

    this.addInputPort({
      name: 'loop',
      label: '循环',
      type: DataType.BOOLEAN,
      required: false,
      defaultValue: false,
      description: '是否循环播放'
    });

    this.addOutputPort({
      name: 'currentTransform',
      label: '当前变换',
      type: DataType.MATRIX4,
      required: false,
      description: '当前时间的变换'
    });

    this.addOutputPort({
      name: 'progress',
      label: '进度',
      type: DataType.NUMBER,
      required: false,
      description: '动画进度 (0-1)'
    });

    this.addOutputPort({
      name: 'isComplete',
      label: '是否完成',
      type: DataType.BOOLEAN,
      required: false,
      description: '动画是否完成'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const startTransform = this.getInput(context, 'startTransform');
    const endTransform = this.getInput(context, 'endTransform');
    const duration = Math.max(0.001, this.getInput(context, 'duration', 1.0));
    const currentTime = this.getInput(context, 'currentTime', 0.0);
    const loop = this.getInput(context, 'loop', false);

    if (!startTransform || !endTransform) {
      throw new Error('变换动画节点需要起始和结束变换');
    }

    let normalizedTime = currentTime / duration;
    let isComplete = false;

    if (loop) {
      normalizedTime = normalizedTime % 1.0;
    } else {
      if (normalizedTime >= 1.0) {
        normalizedTime = 1.0;
        isComplete = true;
      }
    }

    // 插值计算当前变换
    const currentTransform = this.interpolateMatrix4(startTransform, endTransform, normalizedTime);

    this.setOutput(context, 'currentTransform', currentTransform);
    this.setOutput(context, 'progress', normalizedTime);
    this.setOutput(context, 'isComplete', isComplete);
  }

  private interpolateMatrix4(from: any, to: any, factor: number): any {
    const result = [];
    for (let i = 0; i < 16; i++) {
      result[i] = from[i] + (to[i] - from[i]) * factor;
    }
    return result;
  }
}
