/**
 * 渲染系统节点编辑器集成
 * 将第五批次渲染系统节点集成到可视化编辑器中
 */

import { VisualScriptEditor } from '../VisualScriptEditor';
import { NodePalette } from '../components/NodePalette';
import { NodeCategory, DataType } from '../../core/types';
import { registerBatch5RenderingNodes } from '../../nodes/registry/Batch5RenderingNodesRegistry';

/**
 * 渲染系统节点面板配置
 */
export interface RenderingNodesPanelConfig {
  showBasicRendering: boolean;
  showCameraSystem: boolean;
  showLightingSystem: boolean;
  enableDragDrop: boolean;
  enablePreview: boolean;
  groupByCategory: boolean;
}

/**
 * 渲染系统节点编辑器集成类
 */
export class RenderingNodesIntegration {
  private editor: VisualScriptEditor;
  private nodePalette: NodePalette;
  private config: RenderingNodesPanelConfig;

  constructor(editor: VisualScriptEditor, config: Partial<RenderingNodesPanelConfig> = {}) {
    this.editor = editor;
    this.nodePalette = editor.getNodePalette();
    
    // 默认配置
    this.config = {
      showBasicRendering: true,
      showCameraSystem: true,
      showLightingSystem: true,
      enableDragDrop: true,
      enablePreview: true,
      groupByCategory: true,
      ...config
    };
  }

  /**
   * 初始化渲染系统节点集成
   */
  public async initialize(): Promise<void> {
    try {
      console.log('初始化渲染系统节点集成...');

      // 注册节点
      registerBatch5RenderingNodes();

      // 设置节点面板
      await this.setupNodePalette();

      // 设置拖拽功能
      if (this.config.enableDragDrop) {
        this.setupDragAndDrop();
      }

      // 设置预览功能
      if (this.config.enablePreview) {
        this.setupNodePreview();
      }

      // 设置快捷键
      this.setupKeyboardShortcuts();

      console.log('渲染系统节点集成初始化完成');

    } catch (error) {
      console.error('渲染系统节点集成初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置节点面板
   */
  private async setupNodePalette(): Promise<void> {
    // 基础渲染节点组
    if (this.config.showBasicRendering) {
      this.nodePalette.addNodeGroup({
        id: 'basic-rendering',
        title: '基础渲染 (201-220)',
        icon: 'render',
        expanded: true,
        nodes: [
          { type: 'RendererInitialize', name: '渲染器初始化', icon: 'settings' },
          { type: 'RenderTarget', name: '渲染目标', icon: 'target' },
          { type: 'ViewportSet', name: '视口设置', icon: 'crop' },
          { type: 'ClearScreen', name: '清屏操作', icon: 'clear' },
          { type: 'RenderQueue', name: '渲染队列', icon: 'queue' },
          { type: 'RenderBatch', name: '渲染批次', icon: 'batch' },
          { type: 'InstancedRendering', name: '实例化渲染', icon: 'copy' },
          { type: 'FrustumCulling', name: '视锥剔除', icon: 'visibility_off' },
          { type: 'OcclusionCulling', name: '遮挡剔除', icon: 'block' },
          { type: 'LODManagement', name: 'LOD管理', icon: 'tune' },
          { type: 'RenderStats', name: '渲染统计', icon: 'analytics' },
          { type: 'FrameBuffer', name: '帧缓冲', icon: 'memory' },
          { type: 'DepthBuffer', name: '深度缓冲', icon: 'layers' },
          { type: 'StencilBuffer', name: '模板缓冲', icon: 'filter' },
          { type: 'BlendMode', name: '混合模式', icon: 'blend' },
          { type: 'DepthTest', name: '深度测试', icon: 'compare' },
          { type: 'FaceCulling', name: '面剔除', icon: 'flip' },
          { type: 'WireframeRender', name: '线框渲染', icon: 'grid_on' },
          { type: 'PointRender', name: '点渲染', icon: 'scatter_plot' },
          { type: 'RenderState', name: '渲染状态', icon: 'settings_applications' }
        ]
      });
    }

    // 相机系统节点组
    if (this.config.showCameraSystem) {
      this.nodePalette.addNodeGroup({
        id: 'camera-system',
        title: '相机系统 (221-240)',
        icon: 'camera_alt',
        expanded: true,
        nodes: [
          { type: 'CameraCreate', name: '相机创建', icon: 'add_a_photo' },
          { type: 'CameraSwitch', name: '相机切换', icon: 'switch_camera' },
          { type: 'PerspectiveCamera', name: '透视相机', icon: 'camera' },
          { type: 'OrthographicCamera', name: '正交相机', icon: 'crop_square' },
          { type: 'CameraPosition', name: '相机位置', icon: 'place' },
          { type: 'CameraTarget', name: '相机目标', icon: 'gps_fixed' },
          { type: 'CameraFollow', name: '相机跟随', icon: 'follow_the_signs' },
          { type: 'CameraOrbit', name: '相机轨道', icon: 'rotate_right' },
          { type: 'FirstPersonCamera', name: '第一人称相机', icon: 'person' },
          { type: 'ThirdPersonCamera', name: '第三人称相机', icon: 'person_outline' },
          { type: 'CameraAnimation', name: '相机动画', icon: 'movie' }
        ]
      });
    }

    // 光照系统节点组
    if (this.config.showLightingSystem) {
      this.nodePalette.addNodeGroup({
        id: 'lighting-system',
        title: '光照系统 (241-250)',
        icon: 'wb_sunny',
        expanded: true,
        nodes: [
          { type: 'AmbientLight', name: '环境光', icon: 'brightness_medium' },
          { type: 'DirectionalLight', name: '方向光', icon: 'wb_sunny' },
          { type: 'PointLight', name: '点光源', icon: 'lightbulb' },
          { type: 'SpotLight', name: '聚光灯', icon: 'flashlight_on' },
          { type: 'AreaLight', name: '区域光', icon: 'crop_landscape' },
          { type: 'LightIntensity', name: '光照强度', icon: 'brightness_high' },
          { type: 'LightColor', name: '光照颜色', icon: 'palette' },
          { type: 'LightShadow', name: '光照阴影', icon: 'shadow' },
          { type: 'ShadowQuality', name: '阴影质量', icon: 'high_quality' },
          { type: 'ShadowDistance', name: '阴影距离', icon: 'straighten' }
        ]
      });
    }
  }

  /**
   * 设置拖拽功能
   */
  private setupDragAndDrop(): void {
    // 启用节点拖拽到画布
    this.nodePalette.enableDragToCanvas(true);

    // 设置拖拽预览
    this.nodePalette.setDragPreview({
      showNodePreview: true,
      showConnectionHints: true,
      highlightCompatiblePorts: true
    });

    // 监听拖拽事件
    this.nodePalette.onNodeDragStart((nodeType: string) => {
      console.log(`开始拖拽节点: ${nodeType}`);
      this.editor.setDragMode(true);
    });

    this.nodePalette.onNodeDragEnd((nodeType: string, position: { x: number, y: number }) => {
      console.log(`结束拖拽节点: ${nodeType} at`, position);
      this.editor.setDragMode(false);
      
      // 在指定位置创建节点
      this.editor.createNodeAt(nodeType, position);
    });
  }

  /**
   * 设置节点预览功能
   */
  private setupNodePreview(): void {
    // 启用悬停预览
    this.nodePalette.enableHoverPreview(true);

    // 设置预览内容
    this.nodePalette.setPreviewProvider((nodeType: string) => {
      return this.generateNodePreview(nodeType);
    });
  }

  /**
   * 生成节点预览内容
   */
  private generateNodePreview(nodeType: string): any {
    const previewData = {
      'RendererInitialize': {
        description: '初始化WebGL渲染器，配置基本渲染参数如抗锯齿、透明背景等',
        inputs: ['canvas', 'config'],
        outputs: ['renderer', 'success', 'error'],
        example: '用于场景渲染的第一步，创建渲染器实例'
      },
      'CameraCreate': {
        description: '创建新的相机实例，支持透视相机和正交相机',
        inputs: ['type', 'fov', 'aspect', 'near', 'far'],
        outputs: ['camera', 'type'],
        example: '创建透视相机用于3D场景观察'
      },
      'AmbientLight': {
        description: '创建环境光，为场景提供基础的全局照明',
        inputs: ['color', 'intensity', 'enabled'],
        outputs: ['light', 'color', 'intensity'],
        example: '为场景添加柔和的基础照明'
      }
      // 可以为更多节点添加预览信息
    };

    return previewData[nodeType] || {
      description: '渲染系统节点',
      inputs: [],
      outputs: [],
      example: '用于渲染管线的节点'
    };
  }

  /**
   * 设置快捷键
   */
  private setupKeyboardShortcuts(): void {
    const shortcuts = [
      {
        key: 'Ctrl+R',
        description: '快速创建渲染器初始化节点',
        action: () => this.editor.createNode('RendererInitialize')
      },
      {
        key: 'Ctrl+Shift+C',
        description: '快速创建相机节点',
        action: () => this.editor.createNode('CameraCreate')
      },
      {
        key: 'Ctrl+L',
        description: '快速创建环境光节点',
        action: () => this.editor.createNode('AmbientLight')
      }
    ];

    shortcuts.forEach(shortcut => {
      this.editor.addKeyboardShortcut(shortcut.key, shortcut.action, shortcut.description);
    });
  }

  /**
   * 创建渲染管线模板
   */
  public createRenderingPipelineTemplate(): void {
    const template = {
      name: '基础渲染管线',
      description: '包含渲染器、相机、光照的基础渲染管线',
      nodes: [
        {
          type: 'RendererInitialize',
          position: { x: 100, y: 100 },
          properties: {
            antialias: true,
            alpha: false
          }
        },
        {
          type: 'CameraCreate',
          position: { x: 300, y: 100 },
          properties: {
            type: 'PerspectiveCamera',
            fov: 75
          }
        },
        {
          type: 'AmbientLight',
          position: { x: 100, y: 300 },
          properties: {
            intensity: 0.5,
            color: '#ffffff'
          }
        },
        {
          type: 'DirectionalLight',
          position: { x: 300, y: 300 },
          properties: {
            intensity: 1.0,
            castShadow: true
          }
        }
      ],
      connections: [
        {
          from: { nodeId: 'renderer', port: 'renderer' },
          to: { nodeId: 'camera', port: 'renderer' }
        }
      ]
    };

    this.editor.loadTemplate(template);
  }

  /**
   * 获取渲染系统节点统计
   */
  public getStats(): any {
    return {
      basicRenderingNodes: 20,
      cameraSystemNodes: 11,
      lightingSystemNodes: 10,
      totalNodes: 41,
      categories: ['基础渲染', '相机系统', '光照系统']
    };
  }

  /**
   * 销毁集成
   */
  public dispose(): void {
    // 清理事件监听器
    this.nodePalette.removeAllListeners();
    
    // 清理快捷键
    this.editor.clearKeyboardShortcuts();
    
    console.log('渲染系统节点集成已销毁');
  }
}
