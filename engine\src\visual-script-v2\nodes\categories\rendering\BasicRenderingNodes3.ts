/**
 * 第五批次：基础渲染系统节点 (212-220) - 第三部分
 * 完成基础渲染功能节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 212 - 帧缓冲节点
 * 帧缓冲管理
 */
export class FrameBufferNode extends BaseNode {
  public static readonly TYPE = 'FrameBuffer';
  public static readonly TITLE = '帧缓冲';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = FrameBufferNode.TITLE;
    
    // 输入端口
    this.addInputPort('width', DataType.NUMBER, '宽度');
    this.addInputPort('height', DataType.NUMBER, '高度');
    this.addInputPort('format', DataType.STRING, '格式');
    this.addInputPort('type', DataType.STRING, '类型');
    
    // 输出端口
    this.addOutputPort('framebuffer', DataType.OBJECT, '帧缓冲对象');
    this.addOutputPort('colorTexture', DataType.OBJECT, '颜色纹理');
    this.addOutputPort('depthTexture', DataType.OBJECT, '深度纹理');
    
    // 属性
    this.addProperty('samples', DataType.NUMBER, 4, '多重采样');
    this.addProperty('depthBuffer', DataType.BOOLEAN, true, '深度缓冲');
    this.addProperty('stencilBuffer', DataType.BOOLEAN, false, '模板缓冲');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const width = this.getInputValue('width') || 512;
      const height = this.getInputValue('height') || 512;
      const format = this.getInputValue('format') || 'RGBA';
      const type = this.getInputValue('type') || 'UnsignedByte';
      
      // 创建渲染目标作为帧缓冲
      const renderTarget = new THREE.WebGLRenderTarget(width, height, {
        format: THREE[format + 'Format'] || THREE.RGBAFormat,
        type: THREE[type + 'Type'] || THREE.UnsignedByteType,
        samples: this.getProperty('samples'),
        depthBuffer: this.getProperty('depthBuffer'),
        stencilBuffer: this.getProperty('stencilBuffer')
      });
      
      // 如果需要深度纹理
      if (this.getProperty('depthBuffer')) {
        renderTarget.depthTexture = new THREE.DepthTexture();
        renderTarget.depthTexture.format = THREE.DepthFormat;
        renderTarget.depthTexture.type = THREE.UnsignedShortType;
      }
      
      this.setOutputValue('framebuffer', renderTarget);
      this.setOutputValue('colorTexture', renderTarget.texture);
      this.setOutputValue('depthTexture', renderTarget.depthTexture);
      
    } catch (error) {
      console.error('帧缓冲创建失败:', error);
      throw error;
    }
  }
}

/**
 * 213 - 深度缓冲节点
 * 深度缓冲设置
 */
export class DepthBufferNode extends BaseNode {
  public static readonly TYPE = 'DepthBuffer';
  public static readonly TITLE = '深度缓冲';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = DepthBufferNode.TITLE;
    
    // 输入端口
    this.addInputPort('renderer', DataType.OBJECT, '渲染器');
    this.addInputPort('material', DataType.OBJECT, '材质');
    this.addInputPort('enabled', DataType.BOOLEAN, '启用深度测试');
    
    // 输出端口
    this.addOutputPort('depthMaterial', DataType.OBJECT, '深度材质');
    this.addOutputPort('depthTexture', DataType.OBJECT, '深度纹理');
    
    // 属性
    this.addProperty('depthFunc', DataType.STRING, 'LessEqualDepth', '深度函数');
    this.addProperty('depthWrite', DataType.BOOLEAN, true, '深度写入');
    this.addProperty('near', DataType.NUMBER, 0.1, '近平面');
    this.addProperty('far', DataType.NUMBER, 1000, '远平面');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const renderer = this.getInputValue('renderer');
      const material = this.getInputValue('material');
      const enabled = this.getInputValue('enabled') !== false;
      
      if (material) {
        // 设置材质的深度属性
        material.depthTest = enabled;
        material.depthWrite = this.getProperty('depthWrite');
        material.depthFunc = THREE[this.getProperty('depthFunc')] || THREE.LessEqualDepth;
      }
      
      // 创建深度材质
      const depthMaterial = new THREE.MeshDepthMaterial({
        depthPacking: THREE.RGBADepthPacking
      });
      
      // 创建深度纹理
      const depthTexture = new THREE.DepthTexture();
      depthTexture.format = THREE.DepthFormat;
      depthTexture.type = THREE.UnsignedShortType;
      
      this.setOutputValue('depthMaterial', depthMaterial);
      this.setOutputValue('depthTexture', depthTexture);
      
    } catch (error) {
      console.error('深度缓冲设置失败:', error);
      throw error;
    }
  }
}

/**
 * 214 - 模板缓冲节点
 * 模板缓冲操作
 */
export class StencilBufferNode extends BaseNode {
  public static readonly TYPE = 'StencilBuffer';
  public static readonly TITLE = '模板缓冲';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = StencilBufferNode.TITLE;
    
    // 输入端口
    this.addInputPort('material', DataType.OBJECT, '材质');
    this.addInputPort('stencilRef', DataType.NUMBER, '模板参考值');
    this.addInputPort('enabled', DataType.BOOLEAN, '启用模板测试');
    
    // 输出端口
    this.addOutputPort('stencilMaterial', DataType.OBJECT, '模板材质');
    
    // 属性
    this.addProperty('stencilFunc', DataType.STRING, 'AlwaysStencilFunc', '模板函数');
    this.addProperty('stencilFail', DataType.STRING, 'KeepStencilOp', '模板失败操作');
    this.addProperty('stencilZFail', DataType.STRING, 'KeepStencilOp', '深度失败操作');
    this.addProperty('stencilZPass', DataType.STRING, 'ReplaceStencilOp', '深度通过操作');
    this.addProperty('stencilMask', DataType.NUMBER, 0xFF, '模板掩码');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const material = this.getInputValue('material');
      const stencilRef = this.getInputValue('stencilRef') || 1;
      const enabled = this.getInputValue('enabled') !== false;
      
      if (material) {
        // 设置模板测试属性
        material.stencilWrite = enabled;
        material.stencilRef = stencilRef;
        material.stencilFunc = THREE[this.getProperty('stencilFunc')] || THREE.AlwaysStencilFunc;
        material.stencilFail = THREE[this.getProperty('stencilFail')] || THREE.KeepStencilOp;
        material.stencilZFail = THREE[this.getProperty('stencilZFail')] || THREE.KeepStencilOp;
        material.stencilZPass = THREE[this.getProperty('stencilZPass')] || THREE.ReplaceStencilOp;
        material.stencilWriteMask = this.getProperty('stencilMask');
        material.stencilFuncMask = this.getProperty('stencilMask');
      }
      
      this.setOutputValue('stencilMaterial', material);
      
    } catch (error) {
      console.error('模板缓冲设置失败:', error);
      throw error;
    }
  }
}

/**
 * 215 - 混合模式节点
 * 颜色混合模式
 */
export class BlendModeNode extends BaseNode {
  public static readonly TYPE = 'BlendMode';
  public static readonly TITLE = '混合模式';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = BlendModeNode.TITLE;
    
    // 输入端口
    this.addInputPort('material', DataType.OBJECT, '材质');
    this.addInputPort('blending', DataType.STRING, '混合模式');
    
    // 输出端口
    this.addOutputPort('blendedMaterial', DataType.OBJECT, '混合材质');
    
    // 属性
    this.addProperty('blendEquation', DataType.STRING, 'AddEquation', '混合方程');
    this.addProperty('blendSrc', DataType.STRING, 'SrcAlphaFactor', '源混合因子');
    this.addProperty('blendDst', DataType.STRING, 'OneMinusSrcAlphaFactor', '目标混合因子');
    this.addProperty('blendEquationAlpha', DataType.STRING, 'AddEquation', 'Alpha混合方程');
    this.addProperty('blendSrcAlpha', DataType.STRING, 'OneFactor', '源Alpha因子');
    this.addProperty('blendDstAlpha', DataType.STRING, 'OneMinusSrcAlphaFactor', '目标Alpha因子');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const material = this.getInputValue('material');
      const blending = this.getInputValue('blending') || 'NormalBlending';
      
      if (!material) {
        throw new Error('需要材质对象');
      }
      
      // 设置混合模式
      material.blending = THREE[blending] || THREE.NormalBlending;
      
      // 如果是自定义混合，设置详细参数
      if (blending === 'CustomBlending') {
        material.blendEquation = THREE[this.getProperty('blendEquation')] || THREE.AddEquation;
        material.blendSrc = THREE[this.getProperty('blendSrc')] || THREE.SrcAlphaFactor;
        material.blendDst = THREE[this.getProperty('blendDst')] || THREE.OneMinusSrcAlphaFactor;
        material.blendEquationAlpha = THREE[this.getProperty('blendEquationAlpha')] || THREE.AddEquation;
        material.blendSrcAlpha = THREE[this.getProperty('blendSrcAlpha')] || THREE.OneFactor;
        material.blendDstAlpha = THREE[this.getProperty('blendDstAlpha')] || THREE.OneMinusSrcAlphaFactor;
      }
      
      this.setOutputValue('blendedMaterial', material);
      
    } catch (error) {
      console.error('混合模式设置失败:', error);
      throw error;
    }
  }
}

/**
 * 216 - 深度测试节点
 * 深度测试设置
 */
export class DepthTestNode extends BaseNode {
  public static readonly TYPE = 'DepthTest';
  public static readonly TITLE = '深度测试';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = DepthTestNode.TITLE;
    
    // 输入端口
    this.addInputPort('material', DataType.OBJECT, '材质');
    this.addInputPort('enabled', DataType.BOOLEAN, '启用深度测试');
    this.addInputPort('depthFunc', DataType.STRING, '深度函数');
    
    // 输出端口
    this.addOutputPort('testMaterial', DataType.OBJECT, '测试材质');
    
    // 属性
    this.addProperty('depthWrite', DataType.BOOLEAN, true, '深度写入');
    this.addProperty('polygonOffset', DataType.BOOLEAN, false, '多边形偏移');
    this.addProperty('polygonOffsetFactor', DataType.NUMBER, 0, '偏移因子');
    this.addProperty('polygonOffsetUnits', DataType.NUMBER, 0, '偏移单位');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const material = this.getInputValue('material');
      const enabled = this.getInputValue('enabled') !== false;
      const depthFunc = this.getInputValue('depthFunc') || 'LessEqualDepth';
      
      if (!material) {
        throw new Error('需要材质对象');
      }
      
      // 设置深度测试
      material.depthTest = enabled;
      material.depthWrite = this.getProperty('depthWrite');
      material.depthFunc = THREE[depthFunc] || THREE.LessEqualDepth;
      
      // 多边形偏移
      material.polygonOffset = this.getProperty('polygonOffset');
      if (material.polygonOffset) {
        material.polygonOffsetFactor = this.getProperty('polygonOffsetFactor');
        material.polygonOffsetUnits = this.getProperty('polygonOffsetUnits');
      }
      
      this.setOutputValue('testMaterial', material);
      
    } catch (error) {
      console.error('深度测试设置失败:', error);
      throw error;
    }
  }
}

/**
 * 217 - 面剔除节点
 * 面剔除设置
 */
export class FaceCullingNode extends BaseNode {
  public static readonly TYPE = 'FaceCulling';
  public static readonly TITLE = '面剔除';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = FaceCullingNode.TITLE;
    
    // 输入端口
    this.addInputPort('material', DataType.OBJECT, '材质');
    this.addInputPort('side', DataType.STRING, '渲染面');
    
    // 输出端口
    this.addOutputPort('culledMaterial', DataType.OBJECT, '剔除材质');
    
    // 属性
    this.addProperty('shadowSide', DataType.STRING, 'BackSide', '阴影面');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const material = this.getInputValue('material');
      const side = this.getInputValue('side') || 'FrontSide';
      
      if (!material) {
        throw new Error('需要材质对象');
      }
      
      // 设置渲染面
      material.side = THREE[side] || THREE.FrontSide;
      material.shadowSide = THREE[this.getProperty('shadowSide')] || THREE.BackSide;
      
      this.setOutputValue('culledMaterial', material);
      
    } catch (error) {
      console.error('面剔除设置失败:', error);
      throw error;
    }
  }
}

/**
 * 218 - 线框渲染节点
 * 线框模式渲染
 */
export class WireframeRenderNode extends BaseNode {
  public static readonly TYPE = 'WireframeRender';
  public static readonly TITLE = '线框渲染';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = WireframeRenderNode.TITLE;
    
    // 输入端口
    this.addInputPort('material', DataType.OBJECT, '材质');
    this.addInputPort('enabled', DataType.BOOLEAN, '启用线框');
    this.addInputPort('linewidth', DataType.NUMBER, '线宽');
    
    // 输出端口
    this.addOutputPort('wireframeMaterial', DataType.OBJECT, '线框材质');
    
    // 属性
    this.addProperty('wireframeLinecap', DataType.STRING, 'round', '线端样式');
    this.addProperty('wireframeLinejoin', DataType.STRING, 'round', '线连接样式');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const material = this.getInputValue('material');
      const enabled = this.getInputValue('enabled') !== false;
      const linewidth = this.getInputValue('linewidth') || 1;
      
      if (!material) {
        throw new Error('需要材质对象');
      }
      
      // 设置线框模式
      material.wireframe = enabled;
      if (enabled) {
        material.wireframeLinewidth = linewidth;
        material.wireframeLinecap = this.getProperty('wireframeLinecap');
        material.wireframeLinejoin = this.getProperty('wireframeLinejoin');
      }
      
      this.setOutputValue('wireframeMaterial', material);
      
    } catch (error) {
      console.error('线框渲染设置失败:', error);
      throw error;
    }
  }
}
