/**
 * 第五批次：基础渲染系统节点 (201-220)
 * 实现基础渲染功能节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 201 - 渲染器初始化节点
 * 初始化渲染器系统
 */
export class RendererInitializeNode extends BaseNode {
  public static readonly TYPE = 'RendererInitialize';
  public static readonly TITLE = '渲染器初始化';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = RendererInitializeNode.TITLE;
    
    // 输入端口
    this.addInputPort('canvas', DataType.OBJECT, '画布元素');
    this.addInputPort('config', DataType.OBJECT, '渲染配置');
    
    // 输出端口
    this.addOutputPort('renderer', DataType.OBJECT, '渲染器实例');
    this.addOutputPort('success', DataType.BOOLEAN, '初始化成功');
    this.addOutputPort('error', DataType.STRING, '错误信息');
    
    // 属性
    this.addProperty('antialias', DataType.BOOLEAN, true, '抗锯齿');
    this.addProperty('alpha', DataType.BOOLEAN, false, '透明背景');
    this.addProperty('preserveDrawingBuffer', DataType.BOOLEAN, false, '保留绘图缓冲区');
    this.addProperty('powerPreference', DataType.STRING, 'default', '性能偏好');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const canvas = this.getInputValue('canvas');
      const config = this.getInputValue('config') || {};
      
      // 合并配置
      const rendererConfig = {
        antialias: this.getProperty('antialias'),
        alpha: this.getProperty('alpha'),
        preserveDrawingBuffer: this.getProperty('preserveDrawingBuffer'),
        powerPreference: this.getProperty('powerPreference'),
        ...config
      };
      
      // 创建渲染器
      const renderer = new THREE.WebGLRenderer({
        canvas: canvas,
        ...rendererConfig
      });
      
      // 设置基本属性
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.setPixelRatio(window.devicePixelRatio);
      renderer.shadowMap.enabled = true;
      renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      
      this.setOutputValue('renderer', renderer);
      this.setOutputValue('success', true);
      this.setOutputValue('error', '');
      
      console.log('渲染器初始化成功');
      
    } catch (error) {
      console.error('渲染器初始化失败:', error);
      this.setOutputValue('renderer', null);
      this.setOutputValue('success', false);
      this.setOutputValue('error', error.message);
    }
  }
}

/**
 * 202 - 渲染目标节点
 * 设置渲染目标
 */
export class RenderTargetNode extends BaseNode {
  public static readonly TYPE = 'RenderTarget';
  public static readonly TITLE = '渲染目标';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = RenderTargetNode.TITLE;
    
    // 输入端口
    this.addInputPort('renderer', DataType.OBJECT, '渲染器');
    this.addInputPort('target', DataType.OBJECT, '渲染目标');
    this.addInputPort('width', DataType.NUMBER, '宽度');
    this.addInputPort('height', DataType.NUMBER, '高度');
    
    // 输出端口
    this.addOutputPort('renderTarget', DataType.OBJECT, '渲染目标');
    this.addOutputPort('texture', DataType.OBJECT, '纹理');
    
    // 属性
    this.addProperty('format', DataType.STRING, 'RGBA', '像素格式');
    this.addProperty('type', DataType.STRING, 'UnsignedByte', '数据类型');
    this.addProperty('generateMipmaps', DataType.BOOLEAN, true, '生成Mipmap');
    this.addProperty('minFilter', DataType.STRING, 'LinearMipmapLinear', '缩小过滤');
    this.addProperty('magFilter', DataType.STRING, 'Linear', '放大过滤');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const renderer = this.getInputValue('renderer');
      const width = this.getInputValue('width') || 512;
      const height = this.getInputValue('height') || 512;
      
      if (!renderer) {
        throw new Error('需要渲染器实例');
      }
      
      // 创建渲染目标
      const renderTarget = new THREE.WebGLRenderTarget(width, height, {
        format: THREE[this.getProperty('format')] || THREE.RGBAFormat,
        type: THREE[this.getProperty('type')] || THREE.UnsignedByteType,
        generateMipmaps: this.getProperty('generateMipmaps'),
        minFilter: THREE[this.getProperty('minFilter')] || THREE.LinearMipmapLinearFilter,
        magFilter: THREE[this.getProperty('magFilter')] || THREE.LinearFilter
      });
      
      this.setOutputValue('renderTarget', renderTarget);
      this.setOutputValue('texture', renderTarget.texture);
      
    } catch (error) {
      console.error('渲染目标创建失败:', error);
      throw error;
    }
  }
}

/**
 * 203 - 视口设置节点
 * 设置渲染视口
 */
export class ViewportSetNode extends BaseNode {
  public static readonly TYPE = 'ViewportSet';
  public static readonly TITLE = '视口设置';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = ViewportSetNode.TITLE;
    
    // 输入端口
    this.addInputPort('renderer', DataType.OBJECT, '渲染器');
    this.addInputPort('x', DataType.NUMBER, 'X坐标');
    this.addInputPort('y', DataType.NUMBER, 'Y坐标');
    this.addInputPort('width', DataType.NUMBER, '宽度');
    this.addInputPort('height', DataType.NUMBER, '高度');
    
    // 输出端口
    this.addOutputPort('viewport', DataType.OBJECT, '视口信息');
    
    // 属性
    this.addProperty('autoResize', DataType.BOOLEAN, true, '自动调整大小');
    this.addProperty('pixelRatio', DataType.NUMBER, 1, '像素比例');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const renderer = this.getInputValue('renderer');
      const x = this.getInputValue('x') || 0;
      const y = this.getInputValue('y') || 0;
      const width = this.getInputValue('width') || window.innerWidth;
      const height = this.getInputValue('height') || window.innerHeight;
      
      if (!renderer) {
        throw new Error('需要渲染器实例');
      }
      
      // 设置视口
      renderer.setViewport(x, y, width, height);
      
      // 设置像素比例
      const pixelRatio = this.getProperty('pixelRatio');
      if (pixelRatio !== 1) {
        renderer.setPixelRatio(pixelRatio);
      }
      
      // 如果启用自动调整大小
      if (this.getProperty('autoResize')) {
        renderer.setSize(width, height);
      }
      
      const viewport = { x, y, width, height, pixelRatio };
      this.setOutputValue('viewport', viewport);
      
    } catch (error) {
      console.error('视口设置失败:', error);
      throw error;
    }
  }
}

/**
 * 204 - 清屏操作节点
 * 清理屏幕缓冲区
 */
export class ClearScreenNode extends BaseNode {
  public static readonly TYPE = 'ClearScreen';
  public static readonly TITLE = '清屏操作';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = ClearScreenNode.TITLE;
    
    // 输入端口
    this.addInputPort('renderer', DataType.OBJECT, '渲染器');
    this.addInputPort('color', DataType.OBJECT, '清屏颜色');
    
    // 输出端口
    this.addOutputPort('cleared', DataType.BOOLEAN, '清屏完成');
    
    // 属性
    this.addProperty('clearColor', DataType.BOOLEAN, true, '清除颜色缓冲区');
    this.addProperty('clearDepth', DataType.BOOLEAN, true, '清除深度缓冲区');
    this.addProperty('clearStencil', DataType.BOOLEAN, false, '清除模板缓冲区');
    this.addProperty('backgroundColor', DataType.STRING, '#000000', '背景颜色');
    this.addProperty('alpha', DataType.NUMBER, 1.0, '透明度');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const renderer = this.getInputValue('renderer');
      const color = this.getInputValue('color');
      
      if (!renderer) {
        throw new Error('需要渲染器实例');
      }
      
      // 设置清屏颜色
      if (color) {
        renderer.setClearColor(color, this.getProperty('alpha'));
      } else {
        const bgColor = this.getProperty('backgroundColor');
        renderer.setClearColor(bgColor, this.getProperty('alpha'));
      }
      
      // 执行清屏操作
      const clearColor = this.getProperty('clearColor');
      const clearDepth = this.getProperty('clearDepth');
      const clearStencil = this.getProperty('clearStencil');
      
      renderer.clear(clearColor, clearDepth, clearStencil);
      
      this.setOutputValue('cleared', true);
      
    } catch (error) {
      console.error('清屏操作失败:', error);
      this.setOutputValue('cleared', false);
      throw error;
    }
  }
}

/**
 * 205 - 渲染队列节点
 * 管理渲染队列
 */
export class RenderQueueNode extends BaseNode {
  public static readonly TYPE = 'RenderQueue';
  public static readonly TITLE = '渲染队列';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  private renderQueue: any[] = [];

  constructor() {
    super();
    this.title = RenderQueueNode.TITLE;
    
    // 输入端口
    this.addInputPort('object', DataType.OBJECT, '渲染对象');
    this.addInputPort('priority', DataType.NUMBER, '优先级');
    this.addInputPort('clear', DataType.BOOLEAN, '清空队列');
    
    // 输出端口
    this.addOutputPort('queue', DataType.ARRAY, '渲染队列');
    this.addOutputPort('count', DataType.NUMBER, '队列长度');
    
    // 属性
    this.addProperty('sortByPriority', DataType.BOOLEAN, true, '按优先级排序');
    this.addProperty('maxQueueSize', DataType.NUMBER, 1000, '最大队列长度');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const object = this.getInputValue('object');
      const priority = this.getInputValue('priority') || 0;
      const clear = this.getInputValue('clear');
      
      // 清空队列
      if (clear) {
        this.renderQueue = [];
      }
      
      // 添加对象到队列
      if (object) {
        const maxSize = this.getProperty('maxQueueSize');
        if (this.renderQueue.length < maxSize) {
          this.renderQueue.push({
            object,
            priority,
            timestamp: Date.now()
          });
        }
      }
      
      // 按优先级排序
      if (this.getProperty('sortByPriority')) {
        this.renderQueue.sort((a, b) => b.priority - a.priority);
      }
      
      this.setOutputValue('queue', this.renderQueue);
      this.setOutputValue('count', this.renderQueue.length);
      
    } catch (error) {
      console.error('渲染队列操作失败:', error);
      throw error;
    }
  }
}

/**
 * 206 - 渲染批次节点
 * 渲染批次优化
 */
export class RenderBatchNode extends BaseNode {
  public static readonly TYPE = 'RenderBatch';
  public static readonly TITLE = '渲染批次';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  private batches: Map<string, any[]> = new Map();

  constructor() {
    super();
    this.title = RenderBatchNode.TITLE;

    // 输入端口
    this.addInputPort('objects', DataType.ARRAY, '渲染对象数组');
    this.addInputPort('material', DataType.OBJECT, '材质');
    this.addInputPort('geometry', DataType.OBJECT, '几何体');

    // 输出端口
    this.addOutputPort('batches', DataType.ARRAY, '批次数组');
    this.addOutputPort('batchCount', DataType.NUMBER, '批次数量');

    // 属性
    this.addProperty('maxBatchSize', DataType.NUMBER, 100, '最大批次大小');
    this.addProperty('groupByMaterial', DataType.BOOLEAN, true, '按材质分组');
    this.addProperty('groupByGeometry', DataType.BOOLEAN, true, '按几何体分组');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const objects = this.getInputValue('objects') || [];
      const material = this.getInputValue('material');
      const geometry = this.getInputValue('geometry');

      this.batches.clear();

      // 按材质和几何体分组
      for (const obj of objects) {
        let batchKey = 'default';

        if (this.getProperty('groupByMaterial') && obj.material) {
          batchKey += `_mat_${obj.material.uuid}`;
        }

        if (this.getProperty('groupByGeometry') && obj.geometry) {
          batchKey += `_geo_${obj.geometry.uuid}`;
        }

        if (!this.batches.has(batchKey)) {
          this.batches.set(batchKey, []);
        }

        const batch = this.batches.get(batchKey);
        const maxSize = this.getProperty('maxBatchSize');

        if (batch.length < maxSize) {
          batch.push(obj);
        }
      }

      const batchArray = Array.from(this.batches.values());
      this.setOutputValue('batches', batchArray);
      this.setOutputValue('batchCount', batchArray.length);

    } catch (error) {
      console.error('渲染批次处理失败:', error);
      throw error;
    }
  }
}

/**
 * 207 - 实例化渲染节点
 * 实例化渲染优化
 */
export class InstancedRenderingNode extends BaseNode {
  public static readonly TYPE = 'InstancedRendering';
  public static readonly TITLE = '实例化渲染';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  constructor() {
    super();
    this.title = InstancedRenderingNode.TITLE;

    // 输入端口
    this.addInputPort('geometry', DataType.OBJECT, '基础几何体');
    this.addInputPort('material', DataType.OBJECT, '材质');
    this.addInputPort('instances', DataType.ARRAY, '实例数据');
    this.addInputPort('count', DataType.NUMBER, '实例数量');

    // 输出端口
    this.addOutputPort('instancedMesh', DataType.OBJECT, '实例化网格');
    this.addOutputPort('instanceCount', DataType.NUMBER, '实际实例数量');

    // 属性
    this.addProperty('maxInstances', DataType.NUMBER, 1000, '最大实例数量');
    this.addProperty('frustumCulled', DataType.BOOLEAN, false, '视锥体剔除');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const geometry = this.getInputValue('geometry');
      const material = this.getInputValue('material');
      const instances = this.getInputValue('instances') || [];
      const count = this.getInputValue('count') || instances.length;

      if (!geometry || !material) {
        throw new Error('需要几何体和材质');
      }

      const maxInstances = this.getProperty('maxInstances');
      const actualCount = Math.min(count, maxInstances);

      // 创建实例化网格
      const instancedMesh = new THREE.InstancedMesh(geometry, material, actualCount);
      instancedMesh.frustumCulled = this.getProperty('frustumCulled');

      // 设置实例变换矩阵
      const matrix = new THREE.Matrix4();
      for (let i = 0; i < actualCount; i++) {
        const instance = instances[i];
        if (instance) {
          if (instance.position || instance.rotation || instance.scale) {
            matrix.compose(
              instance.position || new THREE.Vector3(),
              instance.rotation || new THREE.Quaternion(),
              instance.scale || new THREE.Vector3(1, 1, 1)
            );
          } else {
            matrix.identity();
          }
        } else {
          matrix.identity();
        }

        instancedMesh.setMatrixAt(i, matrix);
      }

      instancedMesh.instanceMatrix.needsUpdate = true;

      this.setOutputValue('instancedMesh', instancedMesh);
      this.setOutputValue('instanceCount', actualCount);

    } catch (error) {
      console.error('实例化渲染失败:', error);
      throw error;
    }
  }
}
