/**
 * 第五批次节点注册表 (201-250)
 * 基础渲染系统节点注册
 */

import { NodeRegistry } from './NodeRegistry';
import { NodeDefinition, NodeCategory, DataType } from '../../core/types';

// 导入基础渲染节点 (201-220)
import {
  RendererInitializeNode,
  RenderTargetNode,
  ViewportSetNode,
  ClearScreenNode,
  RenderQueueNode,
  RenderBatchNode,
  InstancedRenderingNode
} from '../categories/rendering/BasicRenderingNodes';

import {
  FrustumCullingNode,
  OcclusionCullingNode,
  LODManagementNode,
  RenderStatsNode
} from '../categories/rendering/BasicRenderingNodes2';

import {
  FrameBufferNode,
  DepthBufferNode,
  StencilBufferNode,
  BlendModeNode,
  DepthTestNode,
  FaceCullingNode,
  WireframeRenderNode
} from '../categories/rendering/BasicRenderingNodes3';

import {
  PointRenderNode,
  RenderStateNode
} from '../categories/rendering/BasicRenderingNodes4';

// 导入相机系统节点 (221-240)
import {
  CameraCreateNode,
  CameraSwitchNode,
  PerspectiveCameraNode,
  OrthographicCameraNode,
  CameraPositionNode,
  CameraTargetNode,
  CameraFollowNode
} from '../categories/rendering/CameraSystemNodes';

import {
  CameraOrbitNode,
  FirstPersonCameraNode,
  ThirdPersonCameraNode,
  CameraAnimationNode
} from '../categories/rendering/CameraSystemNodes2';

// 导入光照系统节点 (241-250)
import {
  AmbientLightNode,
  DirectionalLightNode,
  PointLightNode,
  SpotLightNode,
  AreaLightNode,
  LightIntensityNode,
  LightColorNode,
  LightShadowNode,
  ShadowQualityNode,
  ShadowDistanceNode
} from '../categories/rendering/LightingSystemNodes';

/**
 * 基础渲染节点定义 (201-220)
 */
const basicRenderingNodeDefinitions: NodeDefinition[] = [
  {
    type: RendererInitializeNode.TYPE,
    category: NodeCategory.RENDERING,
    name: RendererInitializeNode.TITLE,
    description: '初始化WebGL渲染器，设置基本渲染参数',
    nodeClass: RendererInitializeNode,
    tags: ['渲染器', '初始化', 'WebGL']
  },
  {
    type: RenderTargetNode.TYPE,
    category: NodeCategory.RENDERING,
    name: RenderTargetNode.TITLE,
    description: '创建和管理渲染目标，用于离屏渲染',
    nodeClass: RenderTargetNode,
    tags: ['渲染目标', '离屏渲染', '纹理']
  },
  {
    type: ViewportSetNode.TYPE,
    category: NodeCategory.RENDERING,
    name: ViewportSetNode.TITLE,
    description: '设置渲染视口大小和位置',
    nodeClass: ViewportSetNode,
    tags: ['视口', '渲染区域', '屏幕']
  },
  {
    type: ClearScreenNode.TYPE,
    category: NodeCategory.RENDERING,
    name: ClearScreenNode.TITLE,
    description: '清理屏幕缓冲区，准备新的渲染帧',
    nodeClass: ClearScreenNode,
    tags: ['清屏', '缓冲区', '帧']
  },
  {
    type: RenderQueueNode.TYPE,
    category: NodeCategory.RENDERING,
    name: RenderQueueNode.TITLE,
    description: '管理渲染队列，控制渲染顺序',
    nodeClass: RenderQueueNode,
    tags: ['渲染队列', '排序', '优先级']
  },
  {
    type: RenderBatchNode.TYPE,
    category: NodeCategory.RENDERING,
    name: RenderBatchNode.TITLE,
    description: '渲染批次优化，减少绘制调用',
    nodeClass: RenderBatchNode,
    tags: ['批处理', '优化', '性能']
  },
  {
    type: InstancedRenderingNode.TYPE,
    category: NodeCategory.RENDERING,
    name: InstancedRenderingNode.TITLE,
    description: '实例化渲染，高效渲染大量相同对象',
    nodeClass: InstancedRenderingNode,
    tags: ['实例化', '批量渲染', '性能']
  },
  {
    type: FrustumCullingNode.TYPE,
    category: NodeCategory.RENDERING,
    name: FrustumCullingNode.TITLE,
    description: '视锥体剔除，移除视野外的对象',
    nodeClass: FrustumCullingNode,
    tags: ['剔除', '视锥体', '优化']
  },
  {
    type: OcclusionCullingNode.TYPE,
    category: NodeCategory.RENDERING,
    name: OcclusionCullingNode.TITLE,
    description: '遮挡剔除，移除被遮挡的对象',
    nodeClass: OcclusionCullingNode,
    tags: ['遮挡剔除', '优化', '可见性']
  },
  {
    type: LODManagementNode.TYPE,
    category: NodeCategory.RENDERING,
    name: LODManagementNode.TITLE,
    description: '细节层次管理，根据距离调整模型细节',
    nodeClass: LODManagementNode,
    tags: ['LOD', '细节层次', '性能']
  },
  {
    type: RenderStatsNode.TYPE,
    category: NodeCategory.RENDERING,
    name: RenderStatsNode.TITLE,
    description: '渲染性能统计，监控帧率和绘制调用',
    nodeClass: RenderStatsNode,
    tags: ['统计', '性能', '监控']
  },
  {
    type: FrameBufferNode.TYPE,
    category: NodeCategory.RENDERING,
    name: FrameBufferNode.TITLE,
    description: '帧缓冲管理，创建自定义渲染目标',
    nodeClass: FrameBufferNode,
    tags: ['帧缓冲', '渲染目标', '后处理']
  },
  {
    type: DepthBufferNode.TYPE,
    category: NodeCategory.RENDERING,
    name: DepthBufferNode.TITLE,
    description: '深度缓冲设置，控制深度测试',
    nodeClass: DepthBufferNode,
    tags: ['深度缓冲', '深度测试', 'Z-Buffer']
  },
  {
    type: StencilBufferNode.TYPE,
    category: NodeCategory.RENDERING,
    name: StencilBufferNode.TITLE,
    description: '模板缓冲操作，实现复杂的渲染效果',
    nodeClass: StencilBufferNode,
    tags: ['模板缓冲', '模板测试', '特效']
  },
  {
    type: BlendModeNode.TYPE,
    category: NodeCategory.RENDERING,
    name: BlendModeNode.TITLE,
    description: '颜色混合模式，控制透明度和混合效果',
    nodeClass: BlendModeNode,
    tags: ['混合模式', '透明度', '颜色混合']
  },
  {
    type: DepthTestNode.TYPE,
    category: NodeCategory.RENDERING,
    name: DepthTestNode.TITLE,
    description: '深度测试设置，控制像素深度比较',
    nodeClass: DepthTestNode,
    tags: ['深度测试', '深度比较', '渲染顺序']
  },
  {
    type: FaceCullingNode.TYPE,
    category: NodeCategory.RENDERING,
    name: FaceCullingNode.TITLE,
    description: '面剔除设置，控制正面和背面渲染',
    nodeClass: FaceCullingNode,
    tags: ['面剔除', '背面剔除', '渲染优化']
  },
  {
    type: WireframeRenderNode.TYPE,
    category: NodeCategory.RENDERING,
    name: WireframeRenderNode.TITLE,
    description: '线框渲染模式，显示模型的线框结构',
    nodeClass: WireframeRenderNode,
    tags: ['线框', '调试', '可视化']
  },
  {
    type: PointRenderNode.TYPE,
    category: NodeCategory.RENDERING,
    name: PointRenderNode.TITLE,
    description: '点渲染模式，渲染点云和粒子',
    nodeClass: PointRenderNode,
    tags: ['点渲染', '点云', '粒子']
  },
  {
    type: RenderStateNode.TYPE,
    category: NodeCategory.RENDERING,
    name: RenderStateNode.TITLE,
    description: '渲染状态管理，控制全局渲染设置',
    nodeClass: RenderStateNode,
    tags: ['渲染状态', '全局设置', '配置']
  }
];

/**
 * 相机系统节点定义 (221-240)
 */
const cameraSystemNodeDefinitions: NodeDefinition[] = [
  {
    type: CameraCreateNode.TYPE,
    category: NodeCategory.RENDERING,
    name: CameraCreateNode.TITLE,
    description: '创建新的相机实例，支持透视和正交相机',
    nodeClass: CameraCreateNode,
    tags: ['相机', '创建', '透视', '正交']
  },
  {
    type: CameraSwitchNode.TYPE,
    category: NodeCategory.RENDERING,
    name: CameraSwitchNode.TITLE,
    description: '切换活跃相机，支持平滑过渡',
    nodeClass: CameraSwitchNode,
    tags: ['相机切换', '过渡', '管理']
  },
  {
    type: PerspectiveCameraNode.TYPE,
    category: NodeCategory.RENDERING,
    name: PerspectiveCameraNode.TITLE,
    description: '透视投影相机设置，模拟人眼视觉',
    nodeClass: PerspectiveCameraNode,
    tags: ['透视相机', '投影', 'FOV']
  },
  {
    type: OrthographicCameraNode.TYPE,
    category: NodeCategory.RENDERING,
    name: OrthographicCameraNode.TITLE,
    description: '正交投影相机设置，无透视变形',
    nodeClass: OrthographicCameraNode,
    tags: ['正交相机', '投影', '2D']
  },
  {
    type: CameraPositionNode.TYPE,
    category: NodeCategory.RENDERING,
    name: CameraPositionNode.TITLE,
    description: '相机位置控制，支持平滑移动',
    nodeClass: CameraPositionNode,
    tags: ['相机位置', '移动', '控制']
  },
  {
    type: CameraTargetNode.TYPE,
    category: NodeCategory.RENDERING,
    name: CameraTargetNode.TITLE,
    description: '相机注视目标，控制相机朝向',
    nodeClass: CameraTargetNode,
    tags: ['相机目标', '朝向', 'LookAt']
  },
  {
    type: CameraFollowNode.TYPE,
    category: NodeCategory.RENDERING,
    name: CameraFollowNode.TITLE,
    description: '相机跟随目标，实现跟随效果',
    nodeClass: CameraFollowNode,
    tags: ['相机跟随', '跟踪', '自动']
  },
  {
    type: CameraOrbitNode.TYPE,
    category: NodeCategory.RENDERING,
    name: CameraOrbitNode.TITLE,
    description: '轨道相机控制，围绕目标旋转',
    nodeClass: CameraOrbitNode,
    tags: ['轨道相机', '旋转', '球坐标']
  },
  {
    type: FirstPersonCameraNode.TYPE,
    category: NodeCategory.RENDERING,
    name: FirstPersonCameraNode.TITLE,
    description: '第一人称视角，FPS风格相机控制',
    nodeClass: FirstPersonCameraNode,
    tags: ['第一人称', 'FPS', '鼠标控制']
  },
  {
    type: ThirdPersonCameraNode.TYPE,
    category: NodeCategory.RENDERING,
    name: ThirdPersonCameraNode.TITLE,
    description: '第三人称视角，跟随角色的相机',
    nodeClass: ThirdPersonCameraNode,
    tags: ['第三人称', '角色跟随', '游戏相机']
  },
  {
    type: CameraAnimationNode.TYPE,
    category: NodeCategory.RENDERING,
    name: CameraAnimationNode.TITLE,
    description: '相机动画播放，预设相机运动',
    nodeClass: CameraAnimationNode,
    tags: ['相机动画', '运镜', '电影']
  }
];

/**
 * 光照系统节点定义 (241-250)
 */
const lightingSystemNodeDefinitions: NodeDefinition[] = [
  {
    type: AmbientLightNode.TYPE,
    category: NodeCategory.RENDERING,
    name: AmbientLightNode.TITLE,
    description: '环境光照设置，提供基础照明',
    nodeClass: AmbientLightNode,
    tags: ['环境光', '基础照明', '全局光']
  },
  {
    type: DirectionalLightNode.TYPE,
    category: NodeCategory.RENDERING,
    name: DirectionalLightNode.TITLE,
    description: '方向光源创建，模拟太阳光',
    nodeClass: DirectionalLightNode,
    tags: ['方向光', '太阳光', '平行光']
  },
  {
    type: PointLightNode.TYPE,
    category: NodeCategory.RENDERING,
    name: PointLightNode.TITLE,
    description: '点光源创建，全方向发光',
    nodeClass: PointLightNode,
    tags: ['点光源', '全向光', '灯泡']
  },
  {
    type: SpotLightNode.TYPE,
    category: NodeCategory.RENDERING,
    name: SpotLightNode.TITLE,
    description: '聚光灯创建，锥形光束',
    nodeClass: SpotLightNode,
    tags: ['聚光灯', '锥形光', '手电筒']
  },
  {
    type: AreaLightNode.TYPE,
    category: NodeCategory.RENDERING,
    name: AreaLightNode.TITLE,
    description: '区域光源创建，面光源照明',
    nodeClass: AreaLightNode,
    tags: ['区域光', '面光源', '柔和光']
  },
  {
    type: LightIntensityNode.TYPE,
    category: NodeCategory.RENDERING,
    name: LightIntensityNode.TITLE,
    description: '光照强度调节，控制光源亮度',
    nodeClass: LightIntensityNode,
    tags: ['光照强度', '亮度', '调节']
  },
  {
    type: LightColorNode.TYPE,
    category: NodeCategory.RENDERING,
    name: LightColorNode.TITLE,
    description: '光照颜色设置，控制光源颜色',
    nodeClass: LightColorNode,
    tags: ['光照颜色', '色彩', '调色']
  },
  {
    type: LightShadowNode.TYPE,
    category: NodeCategory.RENDERING,
    name: LightShadowNode.TITLE,
    description: '阴影投射设置，配置光源阴影',
    nodeClass: LightShadowNode,
    tags: ['阴影', '投射', '配置']
  },
  {
    type: ShadowQualityNode.TYPE,
    category: NodeCategory.RENDERING,
    name: ShadowQualityNode.TITLE,
    description: '阴影质量控制，调整阴影效果',
    nodeClass: ShadowQualityNode,
    tags: ['阴影质量', '效果', '性能']
  },
  {
    type: ShadowDistanceNode.TYPE,
    category: NodeCategory.RENDERING,
    name: ShadowDistanceNode.TITLE,
    description: '阴影渲染距离，控制阴影范围',
    nodeClass: ShadowDistanceNode,
    tags: ['阴影距离', '范围', '优化']
  }
];

/**
 * 注册第五批次的所有渲染系统节点
 */
export function registerBatch5RenderingNodes(): void {
  console.log('开始注册第五批次：基础渲染系统节点...');

  const nodeRegistry = NodeRegistry.getInstance();

  // 注册基础渲染节点 (201-220)
  nodeRegistry.registerBatch(basicRenderingNodeDefinitions);
  console.log(`注册了 ${basicRenderingNodeDefinitions.length} 个基础渲染节点`);

  // 注册相机系统节点 (221-240)
  nodeRegistry.registerBatch(cameraSystemNodeDefinitions);
  console.log(`注册了 ${cameraSystemNodeDefinitions.length} 个相机系统节点`);

  // 注册光照系统节点 (241-250)
  nodeRegistry.registerBatch(lightingSystemNodeDefinitions);
  console.log(`注册了 ${lightingSystemNodeDefinitions.length} 个光照系统节点`);

  console.log('第五批次渲染系统节点注册完成！');

  // 输出统计信息
  const stats = nodeRegistry.getStats();
  console.log(`总计注册节点: ${stats.totalNodes} 个`);
  console.log('按分类统计:', Object.fromEntries(stats.byCategory));
}

// 导出所有节点定义
export {
  basicRenderingNodeDefinitions,
  cameraSystemNodeDefinitions,
  lightingSystemNodeDefinitions
};
