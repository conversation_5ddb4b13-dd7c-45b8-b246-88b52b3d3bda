/**
 * 第五批次：相机系统节点 (228-240) - 第二部分
 * 继续实现相机系统功能节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 228 - 相机轨道节点
 * 轨道相机控制
 */
export class CameraOrbitNode extends BaseNode {
  public static readonly TYPE = 'CameraOrbit';
  public static readonly TITLE = '相机轨道';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  private spherical = new THREE.Spherical();
  private target = new THREE.Vector3();

  constructor() {
    super();
    this.title = CameraOrbitNode.TITLE;
    
    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机对象');
    this.addInputPort('target', DataType.OBJECT, '轨道中心');
    this.addInputPort('azimuth', DataType.NUMBER, '方位角');
    this.addInputPort('polar', DataType.NUMBER, '极角');
    this.addInputPort('radius', DataType.NUMBER, '半径');
    
    // 输出端口
    this.addOutputPort('camera', DataType.OBJECT, '相机对象');
    this.addOutputPort('spherical', DataType.OBJECT, '球坐标');
    
    // 属性
    this.addProperty('minRadius', DataType.NUMBER, 1, '最小半径');
    this.addProperty('maxRadius', DataType.NUMBER, 100, '最大半径');
    this.addProperty('minPolar', DataType.NUMBER, 0, '最小极角');
    this.addProperty('maxPolar', DataType.NUMBER, Math.PI, '最大极角');
    this.addProperty('enableDamping', DataType.BOOLEAN, true, '启用阻尼');
    this.addProperty('dampingFactor', DataType.NUMBER, 0.05, '阻尼系数');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const camera = this.getInputValue('camera');
      const target = this.getInputValue('target') || new THREE.Vector3(0, 0, 0);
      const azimuth = this.getInputValue('azimuth');
      const polar = this.getInputValue('polar');
      const radius = this.getInputValue('radius');
      
      if (!camera) {
        throw new Error('需要相机对象');
      }
      
      // 更新目标位置
      this.target.copy(target);
      
      // 从相机位置计算球坐标
      const offset = new THREE.Vector3().subVectors(camera.position, this.target);
      this.spherical.setFromVector3(offset);
      
      // 更新球坐标参数
      if (azimuth !== undefined) {
        this.spherical.theta = azimuth;
      }
      
      if (polar !== undefined) {
        this.spherical.phi = Math.max(
          this.getProperty('minPolar'),
          Math.min(this.getProperty('maxPolar'), polar)
        );
      }
      
      if (radius !== undefined) {
        this.spherical.radius = Math.max(
          this.getProperty('minRadius'),
          Math.min(this.getProperty('maxRadius'), radius)
        );
      }
      
      // 应用阻尼
      if (this.getProperty('enableDamping')) {
        const dampingFactor = this.getProperty('dampingFactor');
        // 这里可以添加阻尼逻辑
      }
      
      // 从球坐标计算新位置
      offset.setFromSpherical(this.spherical);
      camera.position.copy(this.target).add(offset);
      
      // 相机看向目标
      camera.lookAt(this.target);
      
      this.setOutputValue('camera', camera);
      this.setOutputValue('spherical', {
        radius: this.spherical.radius,
        theta: this.spherical.theta,
        phi: this.spherical.phi
      });
      
    } catch (error) {
      console.error('相机轨道控制失败:', error);
      throw error;
    }
  }
}

/**
 * 229 - 第一人称相机节点
 * 第一人称视角
 */
export class FirstPersonCameraNode extends BaseNode {
  public static readonly TYPE = 'FirstPersonCamera';
  public static readonly TITLE = '第一人称相机';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  private euler = new THREE.Euler(0, 0, 0, 'YXZ');
  private velocity = new THREE.Vector3();

  constructor() {
    super();
    this.title = FirstPersonCameraNode.TITLE;
    
    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机对象');
    this.addInputPort('moveForward', DataType.BOOLEAN, '向前移动');
    this.addInputPort('moveBackward', DataType.BOOLEAN, '向后移动');
    this.addInputPort('moveLeft', DataType.BOOLEAN, '向左移动');
    this.addInputPort('moveRight', DataType.BOOLEAN, '向右移动');
    this.addInputPort('mouseX', DataType.NUMBER, '鼠标X移动');
    this.addInputPort('mouseY', DataType.NUMBER, '鼠标Y移动');
    
    // 输出端口
    this.addOutputPort('camera', DataType.OBJECT, '相机对象');
    this.addOutputPort('velocity', DataType.OBJECT, '移动速度');
    
    // 属性
    this.addProperty('moveSpeed', DataType.NUMBER, 10, '移动速度');
    this.addProperty('mouseSensitivity', DataType.NUMBER, 0.002, '鼠标灵敏度');
    this.addProperty('enableJump', DataType.BOOLEAN, true, '启用跳跃');
    this.addProperty('jumpHeight', DataType.NUMBER, 5, '跳跃高度');
    this.addProperty('gravity', DataType.NUMBER, -9.8, '重力');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const camera = this.getInputValue('camera');
      const moveForward = this.getInputValue('moveForward');
      const moveBackward = this.getInputValue('moveBackward');
      const moveLeft = this.getInputValue('moveLeft');
      const moveRight = this.getInputValue('moveRight');
      const mouseX = this.getInputValue('mouseX') || 0;
      const mouseY = this.getInputValue('mouseY') || 0;
      
      if (!camera) {
        throw new Error('需要相机对象');
      }
      
      const moveSpeed = this.getProperty('moveSpeed');
      const mouseSensitivity = this.getProperty('mouseSensitivity');
      
      // 鼠标控制旋转
      this.euler.setFromQuaternion(camera.quaternion);
      this.euler.y -= mouseX * mouseSensitivity;
      this.euler.x -= mouseY * mouseSensitivity;
      this.euler.x = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, this.euler.x));
      camera.quaternion.setFromEuler(this.euler);
      
      // 键盘控制移动
      const direction = new THREE.Vector3();
      
      if (moveForward) direction.z -= 1;
      if (moveBackward) direction.z += 1;
      if (moveLeft) direction.x -= 1;
      if (moveRight) direction.x += 1;
      
      direction.normalize();
      direction.multiplyScalar(moveSpeed);
      
      // 应用相机旋转到移动方向
      direction.applyQuaternion(camera.quaternion);
      
      // 更新相机位置
      camera.position.add(direction);
      
      // 应用重力（简单实现）
      const gravity = this.getProperty('gravity');
      this.velocity.y += gravity * 0.016; // 假设60fps
      camera.position.y += this.velocity.y * 0.016;
      
      // 地面碰撞检测（简单实现）
      if (camera.position.y < 1.6) { // 假设人眼高度1.6米
        camera.position.y = 1.6;
        this.velocity.y = 0;
      }
      
      this.setOutputValue('camera', camera);
      this.setOutputValue('velocity', this.velocity.clone());
      
    } catch (error) {
      console.error('第一人称相机控制失败:', error);
      throw error;
    }
  }
}

/**
 * 230 - 第三人称相机节点
 * 第三人称视角
 */
export class ThirdPersonCameraNode extends BaseNode {
  public static readonly TYPE = 'ThirdPersonCamera';
  public static readonly TITLE = '第三人称相机';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  private idealOffset = new THREE.Vector3();
  private idealLookAt = new THREE.Vector3();
  private currentPosition = new THREE.Vector3();
  private currentLookAt = new THREE.Vector3();

  constructor() {
    super();
    this.title = ThirdPersonCameraNode.TITLE;
    
    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机对象');
    this.addInputPort('target', DataType.OBJECT, '跟随目标');
    this.addInputPort('mouseX', DataType.NUMBER, '鼠标X移动');
    this.addInputPort('mouseY', DataType.NUMBER, '鼠标Y移动');
    
    // 输出端口
    this.addOutputPort('camera', DataType.OBJECT, '相机对象');
    this.addOutputPort('distance', DataType.NUMBER, '距离目标距离');
    
    // 属性
    this.addProperty('distance', DataType.NUMBER, 10, '跟随距离');
    this.addProperty('height', DataType.NUMBER, 5, '相机高度');
    this.addProperty('mouseSensitivity', DataType.NUMBER, 0.01, '鼠标灵敏度');
    this.addProperty('smoothness', DataType.NUMBER, 0.1, '平滑度');
    this.addProperty('collisionDetection', DataType.BOOLEAN, true, '碰撞检测');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const camera = this.getInputValue('camera');
      const target = this.getInputValue('target');
      const mouseX = this.getInputValue('mouseX') || 0;
      const mouseY = this.getInputValue('mouseY') || 0;
      
      if (!camera || !target) {
        throw new Error('需要相机对象和跟随目标');
      }
      
      const targetPosition = target.position || target;
      const distance = this.getProperty('distance');
      const height = this.getProperty('height');
      const mouseSensitivity = this.getProperty('mouseSensitivity');
      const smoothness = this.getProperty('smoothness');
      
      // 计算理想的相机偏移
      const horizontalAngle = mouseX * mouseSensitivity;
      const verticalAngle = mouseY * mouseSensitivity;
      
      this.idealOffset.set(
        Math.sin(horizontalAngle) * distance,
        height + Math.sin(verticalAngle) * distance * 0.5,
        Math.cos(horizontalAngle) * distance
      );
      
      // 理想的观察点
      this.idealLookAt.copy(targetPosition);
      this.idealLookAt.y += height * 0.5;
      
      // 理想的相机位置
      const idealPosition = targetPosition.clone().add(this.idealOffset);
      
      // 碰撞检测
      if (this.getProperty('collisionDetection')) {
        // 这里可以添加射线检测逻辑
        // 简单实现：确保相机不会穿过地面
        if (idealPosition.y < 1) {
          idealPosition.y = 1;
        }
      }
      
      // 平滑插值到理想位置
      this.currentPosition.lerp(idealPosition, smoothness);
      this.currentLookAt.lerp(this.idealLookAt, smoothness);
      
      // 更新相机
      camera.position.copy(this.currentPosition);
      camera.lookAt(this.currentLookAt);
      
      const currentDistance = camera.position.distanceTo(targetPosition);
      
      this.setOutputValue('camera', camera);
      this.setOutputValue('distance', currentDistance);
      
    } catch (error) {
      console.error('第三人称相机控制失败:', error);
      throw error;
    }
  }
}

/**
 * 231 - 相机动画节点
 * 相机动画播放
 */
export class CameraAnimationNode extends BaseNode {
  public static readonly TYPE = 'CameraAnimation';
  public static readonly TITLE = '相机动画';
  public static readonly CATEGORY = NodeCategory.RENDERING;

  private animationMixer: THREE.AnimationMixer | null = null;
  private animationAction: THREE.AnimationAction | null = null;

  constructor() {
    super();
    this.title = CameraAnimationNode.TITLE;
    
    // 输入端口
    this.addInputPort('camera', DataType.OBJECT, '相机对象');
    this.addInputPort('animation', DataType.OBJECT, '动画剪辑');
    this.addInputPort('play', DataType.BOOLEAN, '播放动画');
    this.addInputPort('time', DataType.NUMBER, '动画时间');
    
    // 输出端口
    this.addOutputPort('camera', DataType.OBJECT, '相机对象');
    this.addOutputPort('isPlaying', DataType.BOOLEAN, '是否播放中');
    this.addOutputPort('currentTime', DataType.NUMBER, '当前时间');
    
    // 属性
    this.addProperty('loop', DataType.BOOLEAN, true, '循环播放');
    this.addProperty('speed', DataType.NUMBER, 1.0, '播放速度');
    this.addProperty('autoPlay', DataType.BOOLEAN, false, '自动播放');
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const camera = this.getInputValue('camera');
      const animation = this.getInputValue('animation');
      const play = this.getInputValue('play');
      const time = this.getInputValue('time');
      
      if (!camera) {
        throw new Error('需要相机对象');
      }
      
      // 初始化动画混合器
      if (!this.animationMixer) {
        this.animationMixer = new THREE.AnimationMixer(camera);
      }
      
      // 设置动画剪辑
      if (animation && !this.animationAction) {
        this.animationAction = this.animationMixer.clipAction(animation);
        this.animationAction.setLoop(
          this.getProperty('loop') ? THREE.LoopRepeat : THREE.LoopOnce,
          this.getProperty('loop') ? Infinity : 1
        );
        this.animationAction.timeScale = this.getProperty('speed');
        
        if (this.getProperty('autoPlay')) {
          this.animationAction.play();
        }
      }
      
      // 控制播放
      if (this.animationAction) {
        if (play) {
          this.animationAction.play();
        } else {
          this.animationAction.pause();
        }
        
        // 设置时间
        if (time !== undefined) {
          this.animationAction.time = time;
        }
        
        // 更新动画
        this.animationMixer.update(0.016); // 假设60fps
      }
      
      const isPlaying = this.animationAction ? !this.animationAction.paused : false;
      const currentTime = this.animationAction ? this.animationAction.time : 0;
      
      this.setOutputValue('camera', camera);
      this.setOutputValue('isPlaying', isPlaying);
      this.setOutputValue('currentTime', currentTime);
      
    } catch (error) {
      console.error('相机动画播放失败:', error);
      throw error;
    }
  }
}
