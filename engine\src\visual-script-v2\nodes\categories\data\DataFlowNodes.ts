/**
 * 数据流节点 (181-200)
 * 实现数据处理和流程控制功能
 */

import { BaseNode } from '../../base/BaseNode';
import { NodeCategory, DataType, IExecutionContext } from '../../../core/types';

/**
 * 181 - 数据输入节点
 */
export class DataInputNode extends BaseNode {
  public readonly type = 'data/input';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '数据输入';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'source',
      label: '数据源',
      type: DataType.STRING,
      required: false,
      defaultValue: 'file',
      description: '数据源类型: file, url, database, user'
    });

    this.addInputPort({
      name: 'path',
      label: '路径',
      type: DataType.STRING,
      required: false,
      description: '数据路径或URL'
    });

    this.addInputPort({
      name: 'format',
      label: '格式',
      type: DataType.STRING,
      required: false,
      defaultValue: 'json',
      description: '数据格式: json, xml, csv, binary'
    });

    this.addInputPort({
      name: 'trigger',
      label: '触发',
      type: DataType.TRIGGER,
      required: false,
      description: '触发数据读取'
    });

    this.addOutputPort({
      name: 'data',
      label: '数据',
      type: DataType.ANY,
      required: false,
      description: '读取的数据'
    });

    this.addOutputPort({
      name: 'success',
      label: '成功',
      type: DataType.BOOLEAN,
      required: false,
      description: '是否成功读取'
    });

    this.addOutputPort({
      name: 'error',
      label: '错误',
      type: DataType.STRING,
      required: false,
      description: '错误信息'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const source = this.getInput(context, 'source', 'file');
    const path = this.getInput(context, 'path', '');
    const format = this.getInput(context, 'format', 'json');

    try {
      let data: any = null;
      
      switch (source) {
        case 'file':
          data = await this.readFromFile(path, format);
          break;
        case 'url':
          data = await this.readFromUrl(path, format);
          break;
        case 'database':
          data = await this.readFromDatabase(path);
          break;
        case 'user':
          data = await this.readFromUser();
          break;
      }

      this.setOutput(context, 'data', data);
      this.setOutput(context, 'success', true);
      this.setOutput(context, 'error', '');
    } catch (error) {
      this.setOutput(context, 'data', null);
      this.setOutput(context, 'success', false);
      this.setOutput(context, 'error', error instanceof Error ? error.message : '未知错误');
    }
  }

  private async readFromFile(path: string, format: string): Promise<any> {
    // 模拟文件读取
    return { type: 'file', path, format, data: 'mock_file_data' };
  }

  private async readFromUrl(url: string, format: string): Promise<any> {
    // 模拟URL读取
    return { type: 'url', url, format, data: 'mock_url_data' };
  }

  private async readFromDatabase(query: string): Promise<any> {
    // 模拟数据库读取
    return { type: 'database', query, data: 'mock_db_data' };
  }

  private async readFromUser(): Promise<any> {
    // 模拟用户输入
    return { type: 'user', data: 'mock_user_data' };
  }
}

/**
 * 182 - 数据输出节点
 */
export class DataOutputNode extends BaseNode {
  public readonly type = 'data/output';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '数据输出';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'data',
      label: '数据',
      type: DataType.ANY,
      required: true,
      description: '要输出的数据'
    });

    this.addInputPort({
      name: 'destination',
      label: '目标',
      type: DataType.STRING,
      required: false,
      defaultValue: 'file',
      description: '输出目标: file, url, database, console'
    });

    this.addInputPort({
      name: 'path',
      label: '路径',
      type: DataType.STRING,
      required: false,
      description: '输出路径'
    });

    this.addInputPort({
      name: 'format',
      label: '格式',
      type: DataType.STRING,
      required: false,
      defaultValue: 'json',
      description: '输出格式: json, xml, csv, binary'
    });

    this.addInputPort({
      name: 'trigger',
      label: '触发',
      type: DataType.TRIGGER,
      required: false,
      description: '触发数据输出'
    });

    this.addOutputPort({
      name: 'success',
      label: '成功',
      type: DataType.BOOLEAN,
      required: false,
      description: '是否成功输出'
    });

    this.addOutputPort({
      name: 'error',
      label: '错误',
      type: DataType.STRING,
      required: false,
      description: '错误信息'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const data = this.getInput(context, 'data');
    const destination = this.getInput(context, 'destination', 'file');
    const path = this.getInput(context, 'path', '');
    const format = this.getInput(context, 'format', 'json');

    if (!data) {
      this.setOutput(context, 'success', false);
      this.setOutput(context, 'error', '没有数据可输出');
      return;
    }

    try {
      switch (destination) {
        case 'file':
          await this.writeToFile(data, path, format);
          break;
        case 'url':
          await this.writeToUrl(data, path, format);
          break;
        case 'database':
          await this.writeToDatabase(data, path);
          break;
        case 'console':
          await this.writeToConsole(data);
          break;
      }

      this.setOutput(context, 'success', true);
      this.setOutput(context, 'error', '');
    } catch (error) {
      this.setOutput(context, 'success', false);
      this.setOutput(context, 'error', error instanceof Error ? error.message : '未知错误');
    }
  }

  private async writeToFile(data: any, path: string, format: string): Promise<void> {
    console.log(`写入文件: ${path}, 格式: ${format}, 数据:`, data);
  }

  private async writeToUrl(data: any, url: string, format: string): Promise<void> {
    console.log(`发送到URL: ${url}, 格式: ${format}, 数据:`, data);
  }

  private async writeToDatabase(data: any, query: string): Promise<void> {
    console.log(`写入数据库: ${query}, 数据:`, data);
  }

  private async writeToConsole(data: any): Promise<void> {
    console.log('数据输出:', data);
  }
}

/**
 * 183 - 数据转换节点
 */
export class DataConvertNode extends BaseNode {
  public readonly type = 'data/convert';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '数据转换';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'input',
      label: '输入数据',
      type: DataType.ANY,
      required: true,
      description: '要转换的数据'
    });

    this.addInputPort({
      name: 'fromType',
      label: '源类型',
      type: DataType.STRING,
      required: false,
      defaultValue: 'auto',
      description: '源数据类型'
    });

    this.addInputPort({
      name: 'toType',
      label: '目标类型',
      type: DataType.STRING,
      required: false,
      defaultValue: 'string',
      description: '目标数据类型: string, number, boolean, array, object'
    });

    this.addOutputPort({
      name: 'output',
      label: '输出数据',
      type: DataType.ANY,
      required: false,
      description: '转换后的数据'
    });

    this.addOutputPort({
      name: 'success',
      label: '成功',
      type: DataType.BOOLEAN,
      required: false,
      description: '是否转换成功'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const input = this.getInput(context, 'input');
    const toType = this.getInput(context, 'toType', 'string');

    if (input === undefined || input === null) {
      this.setOutput(context, 'output', null);
      this.setOutput(context, 'success', false);
      return;
    }

    try {
      let output: any;

      switch (toType) {
        case 'string':
          output = String(input);
          break;
        case 'number':
          output = Number(input);
          break;
        case 'boolean':
          output = Boolean(input);
          break;
        case 'array':
          output = Array.isArray(input) ? input : [input];
          break;
        case 'object':
          output = typeof input === 'object' ? input : { value: input };
          break;
        default:
          output = input;
      }

      this.setOutput(context, 'output', output);
      this.setOutput(context, 'success', true);
    } catch (error) {
      this.setOutput(context, 'output', null);
      this.setOutput(context, 'success', false);
    }
  }
}

/**
 * 184 - 数据验证节点
 */
export class DataValidateNode extends BaseNode {
  public readonly type = 'data/validate';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '数据验证';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'data',
      label: '数据',
      type: DataType.ANY,
      required: true,
      description: '要验证的数据'
    });

    this.addInputPort({
      name: 'rules',
      label: '验证规则',
      type: DataType.OBJECT,
      required: false,
      description: '验证规则配置'
    });

    this.addInputPort({
      name: 'required',
      label: '必填',
      type: DataType.BOOLEAN,
      required: false,
      defaultValue: true,
      description: '是否必填'
    });

    this.addInputPort({
      name: 'type',
      label: '类型',
      type: DataType.STRING,
      required: false,
      defaultValue: 'any',
      description: '期望的数据类型'
    });

    this.addOutputPort({
      name: 'isValid',
      label: '有效',
      type: DataType.BOOLEAN,
      required: false,
      description: '数据是否有效'
    });

    this.addOutputPort({
      name: 'errors',
      label: '错误',
      type: DataType.ARRAY,
      required: false,
      description: '验证错误列表'
    });

    this.addOutputPort({
      name: 'validData',
      label: '有效数据',
      type: DataType.ANY,
      required: false,
      description: '验证通过的数据'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const data = this.getInput(context, 'data');
    const rules = this.getInput(context, 'rules', {});
    const required = this.getInput(context, 'required', true);
    const expectedType = this.getInput(context, 'type', 'any');

    const errors: string[] = [];
    let isValid = true;

    // 检查必填
    if (required && (data === null || data === undefined)) {
      errors.push('数据不能为空');
      isValid = false;
    }

    // 检查类型
    if (data !== null && data !== undefined && expectedType !== 'any') {
      const actualType = typeof data;
      if (actualType !== expectedType) {
        errors.push(`期望类型 ${expectedType}，实际类型 ${actualType}`);
        isValid = false;
      }
    }

    // 应用自定义规则
    if (rules && typeof rules === 'object') {
      const ruleErrors = this.applyValidationRules(data, rules);
      errors.push(...ruleErrors);
      if (ruleErrors.length > 0) {
        isValid = false;
      }
    }

    this.setOutput(context, 'isValid', isValid);
    this.setOutput(context, 'errors', errors);
    this.setOutput(context, 'validData', isValid ? data : null);
  }

  private applyValidationRules(data: any, rules: any): string[] {
    const errors: string[] = [];

    if (rules.minLength && typeof data === 'string' && data.length < rules.minLength) {
      errors.push(`字符串长度不能少于 ${rules.minLength}`);
    }

    if (rules.maxLength && typeof data === 'string' && data.length > rules.maxLength) {
      errors.push(`字符串长度不能超过 ${rules.maxLength}`);
    }

    if (rules.min && typeof data === 'number' && data < rules.min) {
      errors.push(`数值不能小于 ${rules.min}`);
    }

    if (rules.max && typeof data === 'number' && data > rules.max) {
      errors.push(`数值不能大于 ${rules.max}`);
    }

    if (rules.pattern && typeof data === 'string') {
      const regex = new RegExp(rules.pattern);
      if (!regex.test(data)) {
        errors.push(`数据不匹配模式 ${rules.pattern}`);
      }
    }

    return errors;
  }
}

/**
 * 185 - 数据过滤节点
 */
export class DataFilterNode extends BaseNode {
  public readonly type = 'data/filter';
  public readonly category = NodeCategory.DATA;

  protected getDefaultName(): string {
    return '数据过滤';
  }

  protected initializePorts(): void {
    this.addInputPort({
      name: 'data',
      label: '数据',
      type: DataType.ARRAY,
      required: true,
      description: '要过滤的数据数组'
    });

    this.addInputPort({
      name: 'condition',
      label: '条件',
      type: DataType.STRING,
      required: false,
      description: '过滤条件表达式'
    });

    this.addInputPort({
      name: 'property',
      label: '属性',
      type: DataType.STRING,
      required: false,
      description: '要过滤的属性名'
    });

    this.addInputPort({
      name: 'value',
      label: '值',
      type: DataType.ANY,
      required: false,
      description: '过滤值'
    });

    this.addInputPort({
      name: 'operator',
      label: '操作符',
      type: DataType.STRING,
      required: false,
      defaultValue: 'equals',
      description: '比较操作符: equals, notEquals, greater, less, contains'
    });

    this.addOutputPort({
      name: 'filtered',
      label: '过滤结果',
      type: DataType.ARRAY,
      required: false,
      description: '过滤后的数据'
    });

    this.addOutputPort({
      name: 'count',
      label: '数量',
      type: DataType.NUMBER,
      required: false,
      description: '过滤后的数据数量'
    });
  }

  public async execute(context: IExecutionContext): Promise<void> {
    const data = this.getInput(context, 'data');
    const condition = this.getInput(context, 'condition');
    const property = this.getInput(context, 'property');
    const value = this.getInput(context, 'value');
    const operator = this.getInput(context, 'operator', 'equals');

    if (!Array.isArray(data)) {
      this.setOutput(context, 'filtered', []);
      this.setOutput(context, 'count', 0);
      return;
    }

    let filtered: any[] = [];

    if (condition) {
      // 使用条件表达式过滤（简化实现）
      filtered = data.filter(item => this.evaluateCondition(item, condition));
    } else if (property && value !== undefined) {
      // 使用属性和值过滤
      filtered = data.filter(item => this.compareValues(item[property], value, operator));
    } else {
      // 过滤掉null和undefined
      filtered = data.filter(item => item !== null && item !== undefined);
    }

    this.setOutput(context, 'filtered', filtered);
    this.setOutput(context, 'count', filtered.length);
  }

  private evaluateCondition(item: any, condition: string): boolean {
    // 简化的条件评估，实际应该使用更安全的表达式解析器
    try {
      // 这里应该实现安全的表达式评估
      return true; // 简化实现
    } catch {
      return false;
    }
  }

  private compareValues(itemValue: any, filterValue: any, operator: string): boolean {
    switch (operator) {
      case 'equals':
        return itemValue === filterValue;
      case 'notEquals':
        return itemValue !== filterValue;
      case 'greater':
        return itemValue > filterValue;
      case 'less':
        return itemValue < filterValue;
      case 'contains':
        return String(itemValue).includes(String(filterValue));
      default:
        return false;
    }
  }
}
