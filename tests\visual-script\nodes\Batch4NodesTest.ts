/**
 * 第四批次节点测试 (151-200)
 * 测试数学计算和数据流节点的功能
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { 
  InterpolateTransformNode,
  TransformConstraintNode,
  DistanceCalculateNode,
  DirectionCalculateNode,
  AngleCalculateNode,
  TransformAnimationNode
} from '../../../engine/src/visual-script-v2/nodes/categories/transform/AdvancedTransformNodes';

import {
  VectorMathNode,
  MatrixMathNode,
  QuaternionMathNode,
  TrigonometryNode,
  RandomGeneratorNode
} from '../../../engine/src/visual-script-v2/nodes/categories/math/AdvancedMathNodes';

import {
  DataInputNode,
  DataOutputNode,
  DataConvertNode,
  DataValidateNode,
  DataFilterNode
} from '../../../engine/src/visual-script-v2/nodes/categories/data/DataFlowNodes';

import { IExecutionContext } from '../../../engine/src/visual-script-v2/core/types';

describe('第四批次节点测试 - 高级变换节点 (151-160)', () => {
  let mockContext: IExecutionContext;

  beforeEach(() => {
    mockContext = {
      inputs: new Map(),
      outputs: new Map(),
      variables: new Map(),
      deltaTime: 0.016,
      totalTime: 0,
      frameCount: 0
    } as IExecutionContext;
  });

  test('插值变换节点 - 基本插值功能', async () => {
    const node = new InterpolateTransformNode();
    
    // 设置输入
    const fromTransform = new Array(16).fill(0);
    fromTransform[0] = fromTransform[5] = fromTransform[10] = fromTransform[15] = 1; // 单位矩阵
    
    const toTransform = new Array(16).fill(0);
    toTransform[0] = toTransform[5] = toTransform[10] = toTransform[15] = 1;
    toTransform[12] = 10; // X位移
    
    mockContext.inputs.set('fromTransform', fromTransform);
    mockContext.inputs.set('toTransform', toTransform);
    mockContext.inputs.set('factor', 0.5);
    mockContext.inputs.set('interpolationType', 'linear');

    await node.execute(mockContext);

    const result = mockContext.outputs.get('result');
    expect(result).toBeDefined();
    expect(result[12]).toBe(5); // 50%插值应该是5
  });

  test('距离计算节点 - 欧几里得距离', async () => {
    const node = new DistanceCalculateNode();
    
    mockContext.inputs.set('position1', [0, 0, 0]);
    mockContext.inputs.set('position2', [3, 4, 0]);
    mockContext.inputs.set('distanceType', 'euclidean');

    await node.execute(mockContext);

    const distance = mockContext.outputs.get('distance');
    const direction = mockContext.outputs.get('direction');
    
    expect(distance).toBe(5); // 3-4-5三角形
    expect(direction).toEqual([0.6, 0.8, 0]); // 归一化方向向量
  });

  test('角度计算节点 - 向量夹角', async () => {
    const node = new AngleCalculateNode();
    
    mockContext.inputs.set('vector1', [1, 0, 0]);
    mockContext.inputs.set('vector2', [0, 1, 0]);
    mockContext.inputs.set('angleUnit', 'degrees');

    await node.execute(mockContext);

    const angle = mockContext.outputs.get('angle');
    const dotProduct = mockContext.outputs.get('dotProduct');
    const crossProduct = mockContext.outputs.get('crossProduct');
    
    expect(angle).toBeCloseTo(90); // 90度夹角
    expect(dotProduct).toBe(0); // 垂直向量点积为0
    expect(crossProduct).toEqual([0, 0, 1]); // 叉积指向Z轴
  });
});

describe('第四批次节点测试 - 高级数学节点 (161-180)', () => {
  let mockContext: IExecutionContext;

  beforeEach(() => {
    mockContext = {
      inputs: new Map(),
      outputs: new Map(),
      variables: new Map(),
      deltaTime: 0.016,
      totalTime: 0,
      frameCount: 0
    } as IExecutionContext;
  });

  test('向量运算节点 - 向量加法', async () => {
    const node = new VectorMathNode();
    
    mockContext.inputs.set('vector1', [1, 2, 3]);
    mockContext.inputs.set('vector2', [4, 5, 6]);
    mockContext.inputs.set('operation', 'add');

    await node.execute(mockContext);

    const result = mockContext.outputs.get('result');
    expect(result).toEqual([5, 7, 9]);
  });

  test('向量运算节点 - 向量点积', async () => {
    const node = new VectorMathNode();
    
    mockContext.inputs.set('vector1', [1, 2, 3]);
    mockContext.inputs.set('vector2', [4, 5, 6]);
    mockContext.inputs.set('operation', 'dot');

    await node.execute(mockContext);

    const scalarResult = mockContext.outputs.get('scalarResult');
    expect(scalarResult).toBe(32); // 1*4 + 2*5 + 3*6 = 32
  });

  test('三角函数节点 - 正弦函数', async () => {
    const node = new TrigonometryNode();
    
    mockContext.inputs.set('angle', 90);
    mockContext.inputs.set('angleUnit', 'degrees');
    mockContext.inputs.set('function', 'sin');

    await node.execute(mockContext);

    const result = mockContext.outputs.get('result');
    expect(result).toBeCloseTo(1, 5); // sin(90°) = 1
  });

  test('随机数生成节点 - 范围随机数', async () => {
    const node = new RandomGeneratorNode();
    
    mockContext.inputs.set('min', 10);
    mockContext.inputs.set('max', 20);
    mockContext.inputs.set('type', 'uniform');

    await node.execute(mockContext);

    const value = mockContext.outputs.get('value');
    const vector = mockContext.outputs.get('vector');
    
    expect(value).toBeGreaterThanOrEqual(10);
    expect(value).toBeLessThanOrEqual(20);
    expect(Array.isArray(vector)).toBe(true);
    expect(vector.length).toBe(3);
  });

  test('四元数运算节点 - 四元数乘法', async () => {
    const node = new QuaternionMathNode();
    
    mockContext.inputs.set('quaternion1', [0, 0, 0, 1]); // 单位四元数
    mockContext.inputs.set('quaternion2', [0, 0, 0.707, 0.707]); // 90度Z轴旋转
    mockContext.inputs.set('operation', 'multiply');

    await node.execute(mockContext);

    const result = mockContext.outputs.get('result');
    expect(result).toBeDefined();
    expect(result.length).toBe(4);
  });
});

describe('第四批次节点测试 - 数据流节点 (181-200)', () => {
  let mockContext: IExecutionContext;

  beforeEach(() => {
    mockContext = {
      inputs: new Map(),
      outputs: new Map(),
      variables: new Map(),
      deltaTime: 0.016,
      totalTime: 0,
      frameCount: 0
    } as IExecutionContext;
  });

  test('数据转换节点 - 字符串转数字', async () => {
    const node = new DataConvertNode();
    
    mockContext.inputs.set('input', '123.45');
    mockContext.inputs.set('toType', 'number');

    await node.execute(mockContext);

    const output = mockContext.outputs.get('output');
    const success = mockContext.outputs.get('success');
    
    expect(output).toBe(123.45);
    expect(success).toBe(true);
  });

  test('数据验证节点 - 必填验证', async () => {
    const node = new DataValidateNode();
    
    mockContext.inputs.set('data', null);
    mockContext.inputs.set('required', true);
    mockContext.inputs.set('type', 'string');

    await node.execute(mockContext);

    const isValid = mockContext.outputs.get('isValid');
    const errors = mockContext.outputs.get('errors');
    
    expect(isValid).toBe(false);
    expect(errors).toContain('数据不能为空');
  });

  test('数据过滤节点 - 数组过滤', async () => {
    const node = new DataFilterNode();
    
    const testData = [
      { name: 'Alice', age: 25 },
      { name: 'Bob', age: 30 },
      { name: 'Charlie', age: 35 }
    ];
    
    mockContext.inputs.set('data', testData);
    mockContext.inputs.set('property', 'age');
    mockContext.inputs.set('value', 30);
    mockContext.inputs.set('operator', 'greater');

    await node.execute(mockContext);

    const filtered = mockContext.outputs.get('filtered');
    const count = mockContext.outputs.get('count');
    
    expect(filtered).toHaveLength(1);
    expect(filtered[0].name).toBe('Charlie');
    expect(count).toBe(1);
  });

  test('数据输入节点 - 模拟文件读取', async () => {
    const node = new DataInputNode();
    
    mockContext.inputs.set('source', 'file');
    mockContext.inputs.set('path', 'test.json');
    mockContext.inputs.set('format', 'json');

    await node.execute(mockContext);

    const data = mockContext.outputs.get('data');
    const success = mockContext.outputs.get('success');
    const error = mockContext.outputs.get('error');
    
    expect(success).toBe(true);
    expect(data).toBeDefined();
    expect(error).toBe('');
  });

  test('数据输出节点 - 模拟控制台输出', async () => {
    const node = new DataOutputNode();
    
    const testData = { message: 'Hello World' };
    mockContext.inputs.set('data', testData);
    mockContext.inputs.set('destination', 'console');

    await node.execute(mockContext);

    const success = mockContext.outputs.get('success');
    const error = mockContext.outputs.get('error');
    
    expect(success).toBe(true);
    expect(error).toBe('');
  });
});

describe('第四批次节点测试 - 集成测试', () => {
  test('数学计算流水线测试', async () => {
    const mockContext: IExecutionContext = {
      inputs: new Map(),
      outputs: new Map(),
      variables: new Map(),
      deltaTime: 0.016,
      totalTime: 0,
      frameCount: 0
    } as IExecutionContext;

    // 1. 生成随机向量
    const randomNode = new RandomGeneratorNode();
    mockContext.inputs.set('min', -1);
    mockContext.inputs.set('max', 1);
    mockContext.inputs.set('type', 'uniform');
    
    await randomNode.execute(mockContext);
    const randomVector = mockContext.outputs.get('vector');

    // 2. 归一化向量
    const vectorNode = new VectorMathNode();
    mockContext.inputs.clear();
    mockContext.inputs.set('vector1', randomVector);
    mockContext.inputs.set('operation', 'normalize');
    
    await vectorNode.execute(mockContext);
    const normalizedVector = mockContext.outputs.get('result');

    // 3. 验证结果
    expect(normalizedVector).toBeDefined();
    const length = Math.sqrt(
      normalizedVector[0] * normalizedVector[0] +
      normalizedVector[1] * normalizedVector[1] +
      normalizedVector[2] * normalizedVector[2]
    );
    expect(length).toBeCloseTo(1, 5); // 归一化向量长度应该为1
  });
});
